<cfcomponent extends="model.admin.admin" output="no">
	<cfscript>
		defaultEvent = 'controller';
	</cfscript>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();			
			
			this.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			this.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
			// local page params ------------------------------------------------------------------------ ::
			this.link.editMember = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');			
			this.languageID = arguments.event.getValue('mc_siteInfo.defaultLanguageID');
			local.referralSettingsQry = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
			if(local.referralSettingsQry.recordCount eq 0) {
				local.data = showNoInstanceMessage();
				return returnAppStruct(local.data,"echo");
			}
			this.referralSettingsQry = local.referralSettingsQry;
			this.referralID = local.referralSettingsQry.referralID;
			this.applicationInstanceID = local.referralSettingsQry.applicationInstanceID;
			this.siteResourceID = local.referralSettingsQry.siteResourceID;	
			this.applicationTypeID = local.referralSettingsQry.applicationTypeID;
			this.emailRecipient = local.referralSettingsQry.emailRecipient;
			this.defaultStateID = local.referralSettingsQry.defaultStateID;
			this.defaultCity = local.referralSettingsQry.defaultCity;
			this.GLAccountID = local.referralSettingsQry.GLAccountID;
			this.clientFeeMemberID = local.referralSettingsQry.clientFeeMemberID;
			this.dspImportClientReferralID = local.referralSettingsQry.dspImportClientReferralID;
			this.feApplicationInstanceID = val(local.referralSettingsQry.feApplicationInstanceID);
			this.dspLegalDescription = val(local.referralSettingsQry.dspLegalDescription);
			this.collectClientFeeFE = val(local.referralSettingsQry.collectClientFeeFE);
			this.allowFeeDiscrepancy = val(local.referralSettingsQry.allowFeeDiscrepancy);
			this.feeDiscrepancyAmt = local.referralSettingsQry.feeDiscrepancyAmt;
			this.collectClientFeeFEOverrideTxt = local.referralSettingsQry.collectClientFeeFEOverrideTxt;
			this.feDspLimitNumOfReferrals = val(local.referralSettingsQry.feDspLimitNumOfReferrals);
			this.referralsSMS = local.referralSettingsQry.referralsSMS;

			this.link.message  	= buildCurrentLink(arguments.event,"message");	
			this.link.home		= buildCurrentLink(arguments.event,"home");			
			this.link.listReferrals		= buildCurrentLink(arguments.event,"listReferrals");	
			this.link.listRetainedCases		= buildCurrentLink(arguments.event,"listRetainedCases");		
			this.link.manageClientReferral		= buildCurrentLink(arguments.event,"manageClientReferral");		
			this.link.managePanel		= buildCurrentLink(arguments.event,"managePanel");		
			this.link.addPanel		= buildCurrentLink(arguments.event,"addPanel");		
			this.link.editPanel		= buildCurrentLink(arguments.event,"editPanel");	
			this.link.savePanel		= buildCurrentLink(arguments.event,"savePanel");	
			this.link.addSubPanel		= buildCurrentLink(arguments.event,"addSubPanel")  & "&mode=direct";		
			this.link.editSubPanel		= buildCurrentLink(arguments.event,"editSubPanel")  & "&mode=direct";	
			this.link.massEditPanels		= buildCurrentLink(arguments.event,"massEditPanels")  & "&mode=direct";	
			this.link.saveSubPanel		= buildCurrentLink(arguments.event,"saveSubPanel");	
			this.link.deletedSubPanel		= buildCurrentLink(arguments.event,"deletedSubPanel")  & "&mode=direct";	
			this.link.addDocument		= buildCurrentLink(arguments.event,"addDocument")  & "&mode=direct";	
			this.link.editDocument		= buildCurrentLink(arguments.event,"editDocument") & "&mode=direct";
			this.link.viewDocument		= buildCurrentLink(arguments.event,"viewDocument") & "&mode=direct";			
			this.link.saveDocument		= buildCurrentLink(arguments.event,"saveDocument") & "&mode=stream";
			this.link.viewClient		= buildCurrentLink(arguments.event,"viewClient");
			this.link.addClient		= buildCurrentLink(arguments.event,"addClient");
			this.link.editClient		= buildCurrentLink(arguments.event,"editClient");
			this.link.saveClient		= buildCurrentLink(arguments.event,"saveClient");
			this.link.completeReferral		= buildCurrentLink(arguments.event,"completeReferral");
			this.link.manageMainSettings		= buildCurrentLink(arguments.event,"manageMainSettings");
			this.link.manageClassifications		= buildCurrentLink(arguments.event,"manageClassifications");
			this.link.manageAgencies		= buildCurrentLink(arguments.event,"manageAgencies");
			this.link.manageSources		= buildCurrentLink(arguments.event,"manageSources");
			this.link.manageStatuses		= buildCurrentLink(arguments.event,"manageStatuses");
			this.link.manageSurveyTypes		= buildCurrentLink(arguments.event,"manageSurveyTypes");
			this.link.editClassification	= buildCurrentLink(arguments.event,"editClassification") & "&mode=direct";
			this.link.viewCaseStatement	= buildCurrentLink(arguments.event,"viewCaseStatement") & "&mode=direct";
			this.link.viewProgressReport	= buildCurrentLink(arguments.event,"viewProgressReport") & "&mode=direct";
			this.link.editSurvey		= buildCurrentLink(arguments.event,"editSurvey")  & "&mode=direct";
			this.link.editAgency		= buildCurrentLink(arguments.event,"editAgency")  & "&mode=direct";	
			this.link.editSource		= buildCurrentLink(arguments.event,"editSource")  & "&mode=direct";
			this.link.editStatus		= buildCurrentLink(arguments.event,"editStatus")  & "&mode=direct";
			this.link.editSurveyType		= buildCurrentLink(arguments.event,"editSurveyType")  & "&mode=direct";

			this.link.editFeeType		= buildCurrentLink(arguments.event,"editFeeType")  & "&mode=direct";
			this.link.previewReferralEmail		= buildCurrentLink(arguments.event,"previewReferralEmail")  & "&mode=direct";
			this.link.previewReferralSMS		= buildCurrentLink(arguments.event,"previewReferralSMS")  & "&mode=direct";
			this.link.saveFeeType		= buildCurrentLink(arguments.event,"saveFeeType") & "&mode=stream";
			
			this.link.resendReferralEmail		= buildCurrentLink(arguments.event,"resendReferralEmail") & "&mode=stream";
			this.link.resendAgencyInfoEmail		= buildCurrentLink(arguments.event,"resendAgencyInfoEmail") & "&mode=stream";
			this.link.showResendReferralEmail	= buildCurrentLink(arguments.event,"showResendReferralEmail") & "&mode=direct";
			
			this.link.permissionsGridShow = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
			this.link.permissionsGridAdd = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='addPermission') & '&mode=direct';
			
			this.link.adjustTransaction	= buildCurrentLink(arguments.event,"adjustTransaction") & "&mode=direct";
			this.link.saveAdjustTransaction	= buildCurrentLink(arguments.event,"saveAdjustTransaction") & "&mode=direct";
			this.link.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			this.link.memSelectReferralGotoLink = buildCurrentLink(arguments.event,"showTransferCaseMemberList") & "&mode=direct";

			local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin');
			this.link.addPayment = local.transAdminTool & "&mca_ta=addPayment&mode=direct";
			this.link.allocatePayment = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct";

			this.link.manageQuestions = buildCurrentLink(arguments.event,"manageQuestions");
			this.link.editQuestion = buildCurrentLink(arguments.event,"editQuestion")  & "&mode=direct";
			this.link.editAnswer = buildCurrentLink(arguments.event,"editAnswer")  & "&mode=direct";
			this.link.editQuestionTree = buildCurrentLink(arguments.event,"editQuestionTree")  & "&mode=direct";
			this.link.getQuestionPanelCount = buildCurrentLink(arguments.event,"getQuestionPanelCount")  & "&mode=stream"
			this.link.getPanelRelatedQuestionsContent = buildCurrentLink(arguments.event,"getPanelRelatedQuestionsContent")  & "&mode=stream";
			this.link.manageScheduledJobs		= buildCurrentLink(arguments.event,"manageScheduledJobs");
			this.link.editScheduleJob = buildCurrentLink(arguments.event,"editScheduleJob")  & "&mode=direct";
			this.link.saveScheduleJob		= buildCurrentLink(arguments.event,"saveScheduleJob") & "&mode=stream";
			this.link.testScheduleJobMail		= buildCurrentLink(arguments.event,"testScheduleJobMail")  & "&mode=direct";
			this.link.emailFeeSubmission		= buildCurrentLink(arguments.event,"emailFeeSubmissionToClient")  & "&mode=direct";

			this.link.manageReferralsLanguage		= buildCurrentLink(arguments.event,"manageReferralsLanguage");			
			this.link.addLanguage		= buildCurrentLink(arguments.event,"addLanguage")  & "&mode=direct";	
			this.link.saveLanguage		= buildCurrentLink(arguments.event,"saveLanguage") & "&mode=stream";
			
			this.link.addNote = buildCurrentLink(arguments.event,"addNote")  & "&mode=direct";
			this.link.saveNote = buildCurrentLink(arguments.event,"saveNote")  & "&mode=direct";
			this.link.showNotes = buildCurrentLink(arguments.event,"showNotes");
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, 
													memberID=session.cfcuser.memberdata.memberID, 
													siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// Run Assigned Method ---------------------------------------------------------------------- 		
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="home" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();			
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<p>Home...</p>				
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="manageClientReferral" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.qryGetClientsViewed = this.objAdminReferrals.getClientsViewed(referralID=this.referralID,memberid=session.cfcuser.memberdata.memberID);
			local.urlString = "";
			
			local.clientListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getClients&referralID=#this.referralID#&mode=stream";
			if(this.feDspLimitNumOfReferrals)
				local.clientLogListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getClientLogs&referralID=#this.referralID#&mode=stream";
		</cfscript>

		<cfset local.firstName = ''>
		<cfset local.lastName = ''>
		<cfset local.email = ''>
		<cfset local.repFirstName = ''>
		<cfset local.repLastName = ''>
		<cfset local.dateCreated = ''>
		<cfset local.dateCreatedTo = ''>
		<cfset local.phoneNumber = ''>
		<cfset local.callUID = ''>
		<cfset local.clientFilter = application.mcCacheManager.sessionGetValue(keyname='clientFilter', defaultValue={})>
		<cfif local.clientFilter.count()>
			<cfset local.firstName = local.clientFilter.firstName>
			<cfset local.lastName = local.clientFilter.lastName>
			<cfset local.email = structKeyExists(local.clientFilter,"email") ? local.clientFilter.email : ''>
			<cfset local.repFirstName = structKeyExists(local.clientFilter,"repFirstName") ? local.clientFilter.repFirstName : ''>
			<cfset local.repLastName = structKeyExists(local.clientFilter,"repLastName") ? local.clientFilter.repLastName : ''>
			<cfset local.dateCreated = local.clientFilter.dateCreated>
			<cfset local.dateCreatedTo = local.clientFilter.dateCreatedTo>
			<cfset local.phoneNumber = local.clientFilter.phoneNumber>
			<cfset local.callUID = local.clientFilter.callUID>				
		</cfif>		
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_clientManagement.cfm" />		
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewClient" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.editMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit')>
		<cfset local.qryClientReferralsDetails = this.objAdminReferrals.getClientReferralsDetails(orgID=arguments.event.getValue('mc_siteInfo.orgID'), clientID=arguments.event.getValue('clientID',0), referralID=this.referralID)>
		
		<cfif local.qryClientReferralsDetails.recordCount EQ 0 >
			<cflocation url="#this.link.manageClientReferral#" addtoken="false">
		<cfelse>
			<cfset var thestate = local.qryClientReferralsDetails.state>
			<cfset local.qryState = application.objCommon.getStates().filter(function(row){
				return arguments.row.stateID eq thestate;
			})>
		</cfif>

		<cfset appendBreadCrumbs(arguments.event,{ link='', text='#local.qryClientReferralsDetails.firstName# #local.qryClientReferralsDetails.lastName#' })>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_client.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="addClient" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();		
			local.rc = arguments.event.getCollection();		
			local.referralID = this.referralID;			
			if (not arguments.event.valueExists("referralID"))
				arguments.event.setValue("referralID",local.referralID);
			local.qryGetReferralSources = this.objAdminReferrals.getReferralSources(referralID=local.referralID, onlyActive=1 );
			local.qryGetReferralFeeTypes = this.objAdminReferrals.getFeeTypes(referralID=local.referralID, isActive=1 );
			local.qryGetMemberInfo = application.objMember.getMemberInfo(memberid=session.cfcuser.memberdata.memberID);
			local.qryGetReferralTypes = this.objAdminReferrals.getReferralTypes(event=arguments.event);
			local.qryGetLanguages 	= this.objAdminReferrals.getReferralLanguages(local.referralID);
			local.qryGetPanelsFilter = this.objAdminReferrals.getPanels(referralID=local.referralID, statusName="Active");
			var currTab = arguments.event.getValue('tab','client');
			local.memberLoggedInID = session.cfcuser.memberdata.memberID;
			local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit');
			local.qryGetClassifications = this.objAdminReferrals.getClassifications(local.referralID);
			local.thiCFCMethod = this.objAdminReferrals.getNavLink(navigationID=arguments.event.getValue('mca_a',0)).cfcMethod;
			local.qryGetAgencies = this.objAdminReferrals.getAgencies(referralID=local.referralID, isActive=1);
			local.referralSettingsQry = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
			local.dspLessFilingFeeCosts = local.referralSettingsQry.dspLessFilingFeeCosts;
			local.allowFeeTypeMgmt = local.referralSettingsQry.allowFeeTypeMgmt;
			local.emailAgencyInfoToClient = local.referralSettingsQry.emailAgencyInfoToClient;			
			local.defaultCallType = local.referralSettingsQry.defaultCallTypeId;
			local.defaultClientSurvey = local.referralSettingsQry.defaultClientSurvey;
			/* add search params to applicationReservedURLParams */
			local.qryFieldsetID = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='referralsearch');
			local.fieldsetInfo = StructNew();
			local.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName;
			local.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat;
			local.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp;
			
			local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
			local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
			variables.applicationReservedURLParams = reReplaceNoCase(listAppend(variables.applicationReservedURLParams,replaceNoCase(arrayToList(local.arrFieldCodes),'<?xml version="1.0" encoding="UTF-8"?>','','ALL')),"\s+","","ALL");						
			local.jsValidation = "";
			local.showReqFlag = false;
			local.defaultMaxNumRecords = 10;
			
			local.qryCountries = application.objCommon.getCountries();
			local.countryCode = QueryFilter(local.qryCountries, function(thisRow) { return arguments.thisRow.countryID EQ application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID; }).countryCode;
			
			/* Get search results */
			arguments.event.setValue('siteResourceID',this.siteResourceID);
			if (currTab eq 'lawyers' or currTab eq 'clientpayment'){
				local.maxNumRecords = local.defaultMaxNumRecords;
				
				arguments.event.setValue('pageNum',int(val(arguments.event.getValue('pageNum',1))));
				local.paging = { 
					link="/?#ReReplaceNoCase(cgi.QUERY_STRING,'&pageNum=[0-9]+','','ALL')#",
					rowsize=local.maxNumRecords,
					currPage=arguments.event.getValue('pageNum'),
					nextPage=arguments.event.getValue('pageNum') + 1,
					prevPage=arguments.event.getValue('pageNum') - 1
				};
				
				local.qryGetSearchResults = this.objAdminReferrals.getSearchResults(event=arguments.event);			
			}
			
			local.qryGetAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'), includeTags=1);
			local.thisMember = structNew();	
			local.baseQueryString = getBaseQueryString(false,true);
			
			/* Client Data */
			local.clientID = 0;
			if (not arguments.event.valueExists('copy'))
				local.clientID = arguments.event.getValue('clientID',0);
			local.firstName = arguments.event.getValue('firstName','');
			local.middleName = arguments.event.getValue('middleName','');
			local.lastName = arguments.event.getValue('lastName','');
			local.businessName = arguments.event.getValue('businessName','');
			local.address1 = arguments.event.getValue('address1','');
			local.address2 = arguments.event.getValue('address2','');
			local.address3 = arguments.event.getValue('address3','');
			local.city = arguments.event.getValue('city','');
			if(not len(local.city)) 
				local.city = this.defaultCity; 			
			local.state = arguments.event.getValue('state',0);
			if(not val(local.state))
				local.state = this.defaultStateID; 
			local.postalCode = arguments.event.getValue('postalCode','');
			local.countryID = arguments.event.getValue('countryID','');
			local.email = arguments.event.getValue('email','');
			local.homePhone = arguments.event.getValue('homePhone','');
			local.cellPhone = arguments.event.getValue('cellPhone','');
			local.alternatePhone = arguments.event.getValue('alternatePhone','');
			local.clientParentID = arguments.event.getValue('clientParentID',0);	
			/* Rep Data */
			local.repID = arguments.event.getValue('repID',0);
			local.repFirstName = arguments.event.getValue('repFirstName','');
			local.repLastName = arguments.event.getValue('repLastName','');
			local.relationToClient = arguments.event.getValue('relationToClient','');
			local.repAddress1 = arguments.event.getValue('repAddress1','');
			local.repAddress2 = arguments.event.getValue('repAddress2','');
			local.repCity = arguments.event.getValue('repCity','');
			local.repState = arguments.event.getValue('repState',0);
			local.repPostalCode = arguments.event.getValue('repPostalCode','');
			local.repEmail = arguments.event.getValue('repEmail','');
			local.repHomePhone = arguments.event.getValue('repHomePhone','');
			local.repCellPhone = arguments.event.getValue('repCellPhone','');
			local.repAlternatePhone = arguments.event.getValue('repAlternatePhone','');
			local.repParentID = arguments.event.getValue('repParentID',0);	
			/* Referral Data */
			local.clientReferralID = arguments.event.getValue('clientReferralID',0);
			local.sourceID = arguments.event.getValue('sourceID','');
			local.feeTypeID = arguments.event.getValue('caseFeeTypeID','');
			local.otherSource = arguments.event.getValue('otherSource',''); 
			local.counselorName = local.qryGetMemberInfo.firstName & " " & local.qryGetMemberInfo.lastName;
			local.counselorNotes = 	arguments.event.getValue('counselorNotes','');
			local.typeID = arguments.event.getValue('typeID',0);
			local.agencyID = arguments.event.getValue('agencyID',0);
			local.agencyName = arguments.event.getValue('agencyName','');
			local.communicateLanguageID = arguments.event.getValue('communicateLanguageID','');
			local.issueDesc = arguments.event.getValue('issueDesc','');
			local.sendSurvey = arguments.event.getValue('sendSurvey',0);
			local.sendNewsBlog = arguments.event.getValue('sendNewsBlog',0);			
			local.statusID = val(local.referralSettingsQry.cpPendingStatusID);
			if(NOT local.statusID) {
				return showRefError(msg='Pending Referral Status is not defined. Please contact administrator.');
			}

			local.qryPendingStatus = this.objAdminReferrals.getStatusByID(statusID=local.statusID);
			local.statusName = local.qryPendingStatus.statusName;
			local.canReferInd = local.qryPendingStatus.canRefer;
			local.referralCanEditClient = local.qryPendingStatus.canEditClient;
			local.referralCanEditFilter = local.qryPendingStatus.canEditFilter;
			local.referralCanEditLawyer = local.qryPendingStatus.canEditLawyer;
			local.isReferred = local.qryPendingStatus.isReferred;
			local.isAgency = local.qryPendingStatus.isAgency;
			local.isPending	= local.qryPendingStatus.isPending;
			local.isCoordination	= local.qryPendingStatus.isCoordination;
			local.isDeleted = local.qryPendingStatus.isDeleted;
			local.isClosed = local.qryPendingStatus.isClosed;
			local.isOpen = local.qryPendingStatus.isOpen;
			local.clientReferralType = "";
			local.qryReferraType = this.objAdminReferrals.getReferralTypeByID(typeID=val(local.typeID));
			local.isLawyerReferral = 0;
			local.pendingClientAttorneyCoordination = false;
			local.allowStatusUpdate = false;
			local.canViewAttorney = false;
			if(val(local.qryReferraType.isReferral)) 
				local.isLawyerReferral = 1;
			local.isAgencyReferral = 0;
			if(val(local.qryReferraType.isAgency)) 		
				local.isAgencyReferral = 1;	
			local.memberID = "";		
			if (not arguments.event.valueExists('copy'))
				local.memberID = arguments.event.getValue('memberID','');
			
			local.callUID = arguments.event.getValue('callUID','');
			/* Case Data */
			local.caseID = 0;
			local.caseStatusID = 0;
			local.caseCanEdit = 0;
			local.caseFees = "";
			local.dateCaseClosed = "";
			/* Filter data */
			local.panelid1 = 0;
			local.subpanelid1 = 0;
			local.panelid2 = 0;
			local.subpanelid2 = 0;
			local.panelid3 = 0;
			local.subpanelid3 = 0;	
			local.prevPanelID = 0;			
			if (not arguments.event.valueExists('copy')){
				local.panelid1 = arguments.event.getValue('panelid1',0);
				local.subpanelid1 = arguments.event.getValue('subpanelid1',0);
				local.panelid2 = arguments.event.getValue('panelid2',0);
				local.subpanelid2 = arguments.event.getValue('subpanelid2',0);
				local.panelid3 = arguments.event.getValue('panelid3',0);
				local.subpanelid3 = arguments.event.getValue('subpanelid3',0);
			}
			/* Payment data */
			local.callPaymentProcessed = 0; 	
			local.clientPaymentSaleRecorded = 0;	
			/* Lawyer data */
			local.memberIdList = "";			
			if (not arguments.event.valueExists('copy'))
				local.memberIdList = arguments.event.getValue('selectedMemberIDs','');
			local.clientRefTitle = "Client";
			local.disableFilterTab = "true";
			local.disableLawyerTab = "true";
			local.qryStates = application.objCommon.getStates();
			local.tabLink = this.link.addClient;
			local.saveLink = this.link.editClient;
			local.formLink = this.link.addClient & "&tab=filters";
			local.resendEmailLink = this.link.resendReferralEmail;
			local.showResendEmailLink = this.link.showResendReferralEmail;
			local.cancelLink = this.link.manageClientReferral;
			if(len(trim(local.thiCFCMethod)) and local.thiCFCMethod neq "addClient")
				local.cancelLink = evaluate("this.link.#local.thiCFCMethod#");	
			if (arguments.event.valueExists('tab') and arguments.event.getValue('tab') eq "filters"){
				local.formLink = this.link.addClient & "&tab=lawyers";
				local.clientRefTitle = "Client: " & arguments.event.getValue('firstName','') & ' ' & arguments.event.getValue('lastName','');
			}
			if (arguments.event.valueExists('tab') and arguments.event.getValue('tab') eq "lawyers"){
				local.formLink = this.link.completeReferral;
				local.clientRefTitle = "Client: " & arguments.event.getValue('firstName','') & ' ' & arguments.event.getValue('lastName','');
				local.disableFilterTab = "false";
			}
			local.caseDocslink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralCaseDocuments&clientReferralID=#local.clientReferralID#&mode=stream";
			
			local.clientFieldNames = "FIRSTNAME,MIDDLENAME,LASTNAME,BUSINESSNAME,ADDRESS1,ADDRESS2,CITY,STATE,POSTALCODE,EMAIL,HOMEPHONE,CELLPHONE,ALTERNATEPHONE,REPFIRSTNAME,REPLASTNAME,RELATIONTOCLIENT,REPADDRESS1,REPADDRESS2,REPCITY,REPSTATE,REPPOSTALCODE,REPEMAIL,REPHOMEPHONE,REPCELLPHONE,REPALTERNATEPHONE,SOURCEID,OTHERSOURCE,COUNSELORNOTES,TYPEID,AGENCYID,AGENCYNAME,COMMUNICATELANGUAGEID,ISSUEDESC,SENDNEWSBLOG,SENDSURVEY#iif(not local.isPending and not local.isClosed and not local.isDeleted,DE(',STATUSID'),DE(''))#";					
			local.filterFieldNames = "PANELID1,PANELID2,PANELID3,SUBPANELID1,SUBPANELID2,SUBPANELID3,PREVPANELID";
			local.clientPaymentFieldNames = "";
			local.lawyerFieldNames = "SELECTEDMEMBERIDS";			
			local.payLater = arguments.event.getValue('payLater',0);
			local.qryPendingCounselorNotes = QueryNew('');
		</cfscript>		
		
		<!--- Initialize local variables and append custom field names to field names list --->
		<cfif isDefined("local.xmlFields.xmlRoot.xmlChildren") and arrayLen(local.xmlFields.xmlRoot.xmlChildren)>	
			<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
				<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = "" />
				<cfif not arguments.event.valueExists('copy')>
					<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = arguments.event.getValue('#local.thisfield.xmlattributes.fieldCode#','') />
				</cfif>
				<cfset local.filterFieldNames = listAppend(local.filterFieldNames,local.thisfield.xmlattributes.fieldCode) />
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>
					<cfset local.filterFieldNames = listAppend(local.filterFieldNames,"#local.thisfield.xmlattributes.fieldCode#_radius") />
					<cfset "local.#local.thisfield.xmlattributes.fieldCode#_radius" = "" />
					<cfif not arguments.event.valueExists('copy')>
						<cfset "local.#local.thisfield.xmlattributes.fieldCode#_radius" = arguments.event.getValue('#local.thisfield.xmlattributes.fieldCode#_radius','') />
					</cfif>
					<cfif local.postalCode NEQ "">
						<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = local.postalCode>
					<cfelseif local.repPostalCode NEQ "">
						<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = local.repPostalCode>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<!--- Initialize local variables and append classification fields to field names list --->
		<cfloop query="local.qryGetClassifications">
			<cfif val(local.qryGetClassifications.allowSearch)>
				<cfset local.filterFieldNames = listAppend(local.filterFieldNames,"mg_gid_#local.qryGetClassifications.groupSetID#") />
				<cfset "local.mg_gid_#local.qryGetClassifications.groupSetID#" = "" />
				<cfif not arguments.event.valueExists('copy')>
					<cfset "local.mg_gid_#local.qryGetClassifications.groupSetID#" = arguments.event.getValue('mg_gid_#local.qryGetClassifications.groupSetID#','') />
				</cfif>
			</cfif>
		</cfloop>

		<!--- get the SRID and permissions of TransactionsAdmin. --->
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfif int(arguments.event.getValue('copyClientReferralID',0)) GT 0>
			<cfset local.extraInformation = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				viewMode='bs4', resourceType='ClientReferrals', areaName='ClientReferrals', csrid=this.siteResourceID, detailID=0, hideAdminOnly=0, itemType='ClientRefCustom', 
				itemID=int(arguments.event.getValue('copyClientReferralID',0)), trItemType='', trApplicationType='')>			
		<cfelse>
			<cfset local.extraInformation = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				viewMode='bs4', resourceType='ClientReferrals', areaName='ClientReferrals', csrid=this.siteResourceID, detailID=0, hideAdminOnly=0, itemType='ClientRefCustom', 
				itemID=0, trItemType='', trApplicationType='')>
		</cfif>
		<cfset local.questionAnswerPath = this.objAdminReferrals.getSearchXMLByClientID(local.clientID)>
		<cfif currTab eq 'lawyers' OR currTab eq 'clientpayment'>
			<cfif local.paging.currpage eq 1>
				<cfset local.currCountStart = 1>
				<cfset local.currCountStop 	= local.paging.rowsize>
			<cfelse>
				<cfset local.currCountStart = local.paging.rowSize * (local.paging.currpage - 1) + 1>
				<cfset local.currCountStop 	= local.paging.rowsize * (local.paging.currpage - 1) + local.qryGetSearchResults.recordCount>
			</cfif>
		</cfif>		
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_clientReferral.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editClient" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">		

		<cfscript>
			var local = structNew();		
			local.objReferrals	= CreateObject("component","model.referrals.referrals");		
			local.historyObj = createObject('component', 'model.system.platform.history');
			local.objAppBaseLink = CreateObject('component', 'model.apploader');
			local.clientReferralID = arguments.event.getValue('clientReferralID','0');
			local.orgID = int(arguments.event.getValue('mc_siteinfo.orgid'));
			if(not val(local.clientReferralID) and not findNoCase("addClient",CGI.HTTP_REFERER)){				
				savecontent variable="local.data" { writeOutput('					
					<div class="alert alert-info alert-dismissible fade show" role="alert">
						You are trying to access an invalid referral.
					</div>
					<div class="col-12 mt-3 pr-0 text-right">
						<a class="btn btn-sm btn-secondary" href="#this.link.manageClientReferral#" role="button">Go Back to Clients List</a>
						<a class="btn btn-sm btn-secondary" href="#this.link.addClient#" role="button">Create new Referral</a>
					</div>				
				'); }	
				return returnAppStruct(local.data,"echo");
			}
			local.referralID = this.referralID;
			local.dspImportClientReferralID = this.dspImportClientReferralID;
			var currTab = arguments.event.getValue('tab','client');
			arguments.event.paramValue('referralID',local.referralID);
			local.currentStatusID = val(arguments.event.getValue('prevStatusID',0));
			local.prevPanelID = val(arguments.event.getValue('prevPanelID',0));
			local.qryClient = this.objAdminReferrals.getClient(clientReferralID=local.clientReferralID);
			if (not local.currentStatusID)
				local.currentStatusID = local.qryClient.statusID;
			local.currentStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=local.referralID, statusID=local.currentStatusID);
			if(local.currentStatusResponseStruct.success){
				local.qryGetCurrentstatus = local.currentStatusResponseStruct.qryStatus;
			} else {
				return showRefError(msg=local.currentStatusResponseStruct.msg);
			}
			local.qryCountries = application.objCommon.getCountries();
			local.countryCode = QueryFilter(local.qryCountries, function(thisRow) { return arguments.thisRow.countryID EQ application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID; }).countryCode;
			
			local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=this.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID);
			local.referralSettingsQry = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));

			if ((arguments.event.valueExists('saveClientBtn') 
				OR arguments.event.valueExists('tabSwitch') 
				OR arguments.event.valueExists('delete')
				OR arguments.event.valueExists('activate')
				OR arguments.event.valueExists('reopen')) 
				and arguments.event.getValue('saveData')){

				// form data outdated
				if (len(arguments.event.getValue('prevLastUpdatedDate','')) AND len(local.qryClient.lastUpdatedDate)) {
					if (dateTimeFormat(local.qryClient.lastUpdatedDate,'ISO8601') NEQ arguments.event.getValue('prevLastUpdatedDate')) {
						local.msg = 'The information you entered may no longer be current. Please refresh the <a href="#this.link.editClient#&clientReferralID=#local.clientReferralID#">page</a> and re-enter your details before submitting.';
						return showRefError(msg=local.msg);
					}
				}

				if(arguments.event.getValue("panelChangedInd","0")){
					local.qryGetClientData = duplicate(local.qryClient);
					local.prevPanelID = val(local.qryGetClientData.panelID);
					local.thisClientID = val(local.qryGetClientData.rootClientID);
					if (not local.thisClientID)
						local.thisClientID = val(local.qryGetClientData.clientID);
					arguments.event.setValue('thisClientID',local.thisClientID);
					local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
					if(not val(local.prevPanelID)){
						for (row in local.qryGetReferralFilterData) {
							if (listFindNoCase("panelid1",local.qryGetReferralFilterData.elementID)){
								local.prevPanelID = local.qryGetReferralFilterData.elementValue;
								break;
							}						
						}
					}
					arguments.event.setValue('prevPanelID',local.prevPanelID);
				}
				/* SAVE MAIN REFERRAL DATA */			
				local.clientReferralID = saveClient(event=arguments.event);

				if(local.referralSettingsQry.allowFeeDiscrepancy)
					local.objReferrals.feeDiscrepancyStatusUpdate(clientReferralID=local.clientReferralID);
					
				// Continue panel change
				if(arguments.event.getValue("panelChangedInd","0")){
					transferReferralPanel(event=arguments.event);
				}
				if(val(arguments.event.getValue('transferMemberId',0)) and val(arguments.event.getValue('memberID',0))){
					transferCaseMemberUpdate(event=arguments.event);
					this.objAdminReferrals.reprocessConditions(orgID=local.orgID, allMembers=1);
					application.objCommon.redirect('#this.link.editClient#&clientReferralID=#local.clientReferralID#');				
				}
				if(val(arguments.event.getValue('statusID')) neq val(arguments.event.getValue('prevStatusID')) and not arguments.event.valueExists('reopen')){
					
					local.newStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=local.referralID, statusID=val(arguments.event.getValue('statusID')));
					local.qryGetNewStatus = local.newStatusResponseStruct.qryStatus;

					// "Pending - Coordination" to "Pending - Referral Sent"
					if (local.qryGetCurrentstatus.primaryStatus EQ 'Pending - Coordination' AND local.qryGetNewStatus.primaryStatus EQ 'Pending - Referral Sent') {
						doEmailReferral(event=arguments.event, callUID=local.qryClient.callUID, clientReferralID=local.clientReferralID);
					}

					local.caseExists = this.objAdminReferrals.checkCaseExists(clientReferralID=arguments.event.getValue('clientReferralID'));
					if(local.qryGetNewStatus.isRetainedCase and not local.qryGetNewStatus.isClosed and not local.caseExists){
						local.caseID = this.objAdminReferrals.insertCase(clientReferralID=arguments.event.getValue('clientReferralID'), panelID=arguments.event.getValue('panelid1',0));
						if (val(arguments.event.getValue('sendEmailInd',0)))
							local.objReferrals.doEmailRetainCase(event=arguments.event);
					}
					local.thisChange =  [{ITEM="Status",OLDVALUE=local.qryGetCurrentstatus.statusName,NEWVALUE=local.qryGetNewStatus.statusName}];
					local.historyObj.addReferralUpdateHistory(orgID=local.orgID, clientReferralID=int(local.clientReferralID),
						actorMemberID=int(session.cfcuser.memberdata.memberID), mainMessage="Referral Status Changed (Admin)", changes=local.thisChange);
					
					// charge consultation fee
					if(local.referralSettingsQry.collectClientFeeFE EQ 1 AND val(local.qryGetNewStatus.isConsultation)){
						local.qryGetPanelDetails = this.objAdminReferrals.getPanelByID(panelID=arguments.event.getValue('panelid1',0));
						
						if(val(local.qryGetPanelDetails.clientReferralAmount) AND NOT this.objAdminReferrals.isClientReferralFeeSaleExists(orgID=local.orgID,clientReferralID=arguments.event.getValue('clientReferralID'))){
							arguments.event.setValue('clientReferralAmount',local.qryGetPanelDetails.clientReferralAmount);
							local.consultationsaleStruct = structNew();

							local.clientName = arguments.event.getValue('firstName') & " " & arguments.event.getValue('lastName');

							local.consultationsaleStruct = this.objAdminReferrals.recordConsultationSale(
								amount=arguments.event.getValue('clientReferralAmount',0.00),
								panelID=arguments.event.getValue('panelid1',0),
								referralID=arguments.event.getValue('referralID'),
								clientReferralID=arguments.event.getValue('clientReferralID'),
								callUID=arguments.event.getValue('callUID'),
								siteID=arguments.event.getValue('mc_siteInfo.siteID'),
								orgID=local.orgID,
								memberID=arguments.event.getValue('memberID'),
								clientName=local.clientName
							);
						}
					}
				}
				if (arguments.event.valueExists('collectedFee') and val(arguments.event.getValue('collectedFee'))){
					local.saleStruct = structNew();
					local.saleStruct = local.objReferrals.recordSale(event=arguments.event);
					local.objReferrals.doEmailCaseFeeCollect(event=arguments.event);
					if(local.referralSettingsQry.allowFeeDiscrepancy)
						local.objReferrals.feeDiscrepancyStatusUpdate(clientReferralID=local.clientReferralID);
					this.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=local.clientReferralID);
					application.objCommon.redirect('#this.link.editClient#&clientReferralID=#local.clientReferralID#&tc=#getTickCount()###feesInfo');	
				}
				if (arguments.event.valueExists('clientReferralAmount') and val(arguments.event.getValue('sr',0)) and NOT val(arguments.event.getValue('payLater',0))){
					local.qryGetPaymentData = this.objAdminReferrals.getClientPaymentData(clientID=arguments.event.getValue('clientID'));
					if(not local.qryGetPaymentData.recordCount){
						local.saleStruct = structNew();
						local.saleStruct = this.objAdminReferrals.recordClientSale(event=arguments.event);
					}
				}
				if (arguments.event.valueExists('delete')){
					
					local.deletedStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=local.referralID, isDeleted=1);
					if(local.deletedStatusResponseStruct.success){
						local.qryDeletedStatus = local.deletedStatusResponseStruct.qryStatus;
					} else {
						return showRefError(msg='Deleted Referral Statuses are not defined. Please contact administrator.');
					}
					
					this.objAdminReferrals.updateClientReferralStatus(clientReferralID=local.clientReferralID, statusID=local.qryDeletedStatus.clientReferralStatusID);
					local.thisChange =  [{ITEM="Status",OLDVALUE=local.qryGetCurrentstatus.statusName,NEWVALUE=local.qryDeletedStatus.statusName}];
					local.historyObj.addReferralUpdateHistory(orgID=local.orgID, clientReferralID=int(local.clientReferralID),
						actorMemberID=int(session.cfcuser.memberdata.memberID), mainMessage="Referral Status Changed", changes=local.thisChange);	
					local.qryGetPaymentData = this.objAdminReferrals.getClientPaymentData(clientID=arguments.event.getValue('clientID'));
					if(local.qryGetPaymentData.recordCount){
						this.objAdminReferrals.removeClientPaymentInfo(mcproxy_orgID=local.orgID, mcproxy_siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), 
							clientID=arguments.event.getValue('clientID'), referralID=local.referralID);
						this.objAdminReferrals.adjustClientReferralFeeToZero(siteID=arguments.event.getValue('mc_siteInfo.siteID'), clientID=arguments.event.getValue('clientID'));
					}
					this.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=local.clientReferralID);
					application.objCommon.redirect('#this.link.manageClientReferral#');
				}
				if (arguments.event.valueExists('activate')){
					local.newStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=local.referralID, isCoordination=0, isOpen=1, isReferred=0, isRetainedCase=0);
					if(local.newStatusResponseStruct.success){
						local.qryNewStatus = local.newStatusResponseStruct.qryStatus;
					} else {
						return showRefError(msg='Open Referral Status is not defined. Please contact administrator.');
					}

					this.objAdminReferrals.updateClientReferralStatus(clientReferralID=local.clientReferralID, statusID=local.qryNewStatus.clientReferralStatusID);
					local.thisChange =  [{ITEM="Status",OLDVALUE=local.qryGetCurrentstatus.statusName,NEWVALUE=local.qryNewStatus.statusName}];
					local.historyObj.addReferralUpdateHistory(orgID=local.orgID, clientReferralID=int(local.clientReferralID),
						actorMemberID=int(session.cfcuser.memberdata.memberID), mainMessage="Referral Status Changed", changes=local.thisChange);
				}				
				if (arguments.event.valueExists('reopen')){
					if (val(arguments.event.getValue('caseID')))
						local.isRetainedCase = 1;
					else
						local.isRetainedCase = 0;

					local.newStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=local.referralID, isOpen=1, isReferred=1, isRetainedCase=local.isRetainedCase)
					if(local.newStatusResponseStruct.success){
						local.qryNewStatus = local.newStatusResponseStruct.qryStatus;
					} else {
						return showRefError(msg='Open '& IIF(val(local.isRetainedCase),DE('Retained'),DE('')) &'Referral Statuses are not defined. Please contact administrator.');
					}

					this.objAdminReferrals.updateClientReferralStatus(clientReferralID=local.clientReferralID, statusID=local.qryNewStatus.clientReferralStatusID);
					local.thisChange =  [{ITEM="Status",OLDVALUE=local.qryGetCurrentstatus.statusName,NEWVALUE=local.qryNewStatus.statusName}];
					local.historyObj.addReferralUpdateHistory(orgID=local.orgID, clientReferralID=int(local.clientReferralID),
						actorMemberID=int(session.cfcuser.memberdata.memberID), mainMessage="Referral Status Changed", changes=local.thisChange);					
					doEmailReopenedReferral(event=arguments.event, clientReferralID=local.clientReferralID);
				}

				// reprocess conditions after possible saveClient/feeDiscrepancyStatusUpdate/insertCase/updateClientReferralStatus calls
				this.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=local.clientReferralID);

				if (arguments.event.valueExists('cfid') and val(arguments.event.getValue('cfid')) and arguments.event.valueExists('tidlist'))
					this.objAdminReferrals.removeFeesRow(collectedFeeID=arguments.event.getValue('cfid'), saleTIDList=arguments.event.getValue('tidlist'), adjustToZero=arguments.event.getValue('atz'), siteID=arguments.event.getValue('mc_siteinfo.siteid'));
				if(arguments.event.getValue('isReferred',0) and arguments.event.getValue('tab','client') eq 'client'){
					application.objCommon.redirect('#this.link.editClient#&clientReferralID=#local.clientReferralID#');
				}
			}
			local.rc = arguments.event.getCollection();				
			local.referralID = this.referralID;
			local.isTransfer = 0;
			local.qryGetReferralSources = this.objAdminReferrals.getReferralSources(referralID=local.referralID, onlyActive=1);
			local.qryGetReferralFeeTypes = this.objAdminReferrals.getFeeTypes(referralID=local.referralID, isActive=1 );
			local.qryGetReferralTypes = this.objAdminReferrals.getReferralTypes(event=arguments.event);
			local.qryGetLanguages 	= this.objAdminReferrals.getReferralLanguages(local.referralID);
			local.qryGetPanelsFilter = this.objAdminReferrals.getPanels(referralID=local.referralID, statusName="Active");
			local.qryGetPanels = this.objAdminReferrals.getPanels(referralID=local.referralID);
			local.memberLoggedInID = session.cfcuser.memberdata.memberID;
			arguments.event.setValue('referralID',local.referralID);
			local.qryGetClientData = this.objAdminReferrals.getClient(clientReferralID=local.clientReferralID);
			local.thisClientID = val(local.qryGetClientData.rootClientID);
			if (not local.thisClientID)
				local.thisClientID = val(local.qryGetClientData.clientID);
			local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
			local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit');
			local.qryGetReferralMembers = this.objAdminReferrals.getReferralMembers(callUID=local.qryGetClientData.callUID);

			local.qryGetReferralStatus = this.objAdminReferrals.getClientReferralStatus(referralID=local.referralID).qryStatus;

			local.qryGetCaseFees = this.objAdminReferrals.getCaseFees(caseID=val(local.qryGetClientData.caseID));
			local.qryGetCaseFeesTotals = local.objReferrals.getFeesTotals(qryItems=local.qryGetCaseFees);
			if (val(local.qryGetClientData.caseID) and not local.qryGetClientData.isClosed){
				local.params = {referralID=local.referralID, isRetainedCase=1};
				local.isTransfer = 1;
			}else{
				local.params = {referralID=local.referralID, isRetainedCase=1, isClosed=1};
			}

			local.caseStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(argumentCollection=local.params);
			if(local.caseStatusResponseStruct.success){
				local.qryGetCaseStatuses = local.caseStatusResponseStruct.qryStatus;
			} else {
				return showRefError(msg='Retained '&IIF(val(local.qryGetClientData.caseID) and not local.qryGetClientData.isClosed,DE(''),DE('Closed'))&' Referral Statuses are not defined. Please contact administrator.');
			}
			
			local.qryGetClassifications = this.objAdminReferrals.getClassifications(local.referralID);
			local.thiCFCMethod = this.objAdminReferrals.getNavLink(navigationID=arguments.event.getValue('mca_a',0)).cfcMethod;
			local.qryGetAgencies = this.objAdminReferrals.getAgencies(referralID=local.referralID, isActive=1);
			
			local.dspLessFilingFeeCosts = local.referralSettingsQry.dspLessFilingFeeCosts;
			local.allowFeeTypeMgmt = local.referralSettingsQry.allowFeeTypeMgmt;
			local.emailAgencyInfoToClient = local.referralSettingsQry.emailAgencyInfoToClient;
			local.clientFeeMemberID = local.referralSettingsQry.clientFeeMemberID;
			local.collectClientFeeFE = local.referralSettingsQry.collectClientFeeFE;
			local.allowFeeDiscrepancy = local.referralSettingsQry.allowFeeDiscrepancy;
			local.feeDiscrepancyAmt = local.referralSettingsQry.feeDiscrepancyAmt;
			local.collectClientFeeFEOverrideTxt = local.referralSettingsQry.collectClientFeeFEOverrideTxt;
			
			local.isSiteAdminRights = arguments.event.getValue('mc_admintoolInfo.myRights.view');

			local.assignmentAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='ReferralsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			
			local.referralAdminRights = buildRightAssignments(siteResourceID=local.assignmentAdminSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			local.isCounselorReassignRights = 0;
			local.counselorSelectGroupID = local.referralSettingsQry.counselorGroupID;
			if(StructKeyExists(local.referralAdminRights,"ReferralSysAssignmentsAdministrator")){
				local.isCounselorReassignRights = local.referralAdminRights.ReferralSysAssignmentsAdministrator;
			}
			local.memSelectLink = this.link.memSelectGotoLink;
			
			/* add search params to applicationReservedURLParams */
			local.qryFieldsetID = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='referralsearch');
			local.fieldsetInfo = structNew();
			local.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName;
			local.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat;
			local.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp;

			local.isPrinterFiendly = 0;
			if(arguments.event.valueExists("print"))
				local.isPrinterFiendly = 1;

			local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
			local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
			variables.applicationReservedURLParams = reReplaceNoCase(listAppend(variables.applicationReservedURLParams,replaceNoCase(arrayToList(local.arrFieldCodes),'<?xml version="1.0" encoding="UTF-8"?>','','ALL')),"\s+","","ALL");						
			local.jsValidation = "";
			local.showReqFlag = false;
			local.defaultMaxNumRecords = 10;
			
			arguments.event.setValue('siteResourceID',this.siteResourceID);		
			local.qryGetAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=local.orgID, includeTags=1);
			local.thisMember = structNew();
			local.baseQueryString = getBaseQueryString(false,true);

			/* Get preliminary search results and payment status */
			local.qryGetPaymentData = this.objAdminReferrals.getClientPaymentData(clientID=local.qryGetClientData.clientID);
			local.qryGetPaymentDataTotals = this.objAdminReferrals.getClientFeesTotals(qryItems=local.qryGetPaymentData);
			local.clientPaymentSaleRecorded = 0;	
			if(local.qryGetPaymentData.recordCount)
				local.clientPaymentSaleRecorded = 1;			
			if (currTab eq 'clientpayment'){
				arguments.event.paramValue('searchFieldsetArea','referralsearch');
				arguments.event.paramValue('resultFieldsetArea','clientfeeresult');
			}
			
			/* Get search results */
			if (currTab eq 'lawyers' OR currTab eq 'clientpayment') {
				local.maxNumRecords = local.defaultMaxNumRecords;
				
				arguments.event.setValue('pageNum',int(val(arguments.event.getValue('pageNum',1))));
				local.paging = { 
					link="/?#ReReplaceNoCase(cgi.QUERY_STRING,'&pageNum=[0-9]+','','ALL')#",
					rowsize=local.maxNumRecords,
					currPage=arguments.event.getValue('pageNum'),
					nextPage=arguments.event.getValue('pageNum') + 1,
					prevPage=arguments.event.getValue('pageNum') - 1
				};
				arguments.event.setValue('paging',local.paging);
				
				local.qryGetSearchResults = this.objAdminReferrals.getSearchResults(event=arguments.event);			
				local.arrAllClassifications = this.objAdminReferrals.getClassificationsForMemberIDList(referralID=this.referralID, orgID=local.orgID, memberIDList=valueList(local.qryGetSearchResults.memberid));
				local.qryMemberPanels = this.objAdminReferrals.getPanelsForMemberIDList(referralID=this.referralID, memberIDList=valueList(local.qryGetSearchResults.memberid));			
			}

			/* Client Data */
			local.clientID = local.qryGetClientData.clientID;
			local.firstName = local.qryGetClientData.firstName;
			local.middleName = local.qryGetClientData.middleName;
			local.lastName = local.qryGetClientData.lastName;
			local.clientName = local.qryGetClientData.clientName;
			local.businessName = local.qryGetClientData.businessName;
			local.address1 = local.qryGetClientData.address1;
			local.address2 = local.qryGetClientData.address2;
			local.city = local.qryGetClientData.city;
			local.state = local.qryGetClientData.state;
			local.postalCode = local.qryGetClientData.postalCode;
			local.email = local.qryGetClientData.email;
			local.homePhone = local.qryGetClientData.homePhone;
			local.cellPhone = local.qryGetClientData.cellPhone;
			local.alternatePhone = local.qryGetClientData.alternatePhone;
			local.homePhoneE164 = local.qryGetClientData.homePhoneE164;
			local.cellPhoneE164 = local.qryGetClientData.cellPhoneE164;
			local.alternatePhoneE164 = local.qryGetClientData.alternatePhoneE164;
			local.clientParentID = local.qryGetClientData.clientParentID;	
			/* Rep Data */
			local.repID = arguments.event.getValue('repID',0);
			if(val(local.qryGetClientData.repID))
				local.repID = local.qryGetClientData.repID;
			local.repFirstName = local.qryGetClientData.repFirstName;
			local.repLastName = local.qryGetClientData.repLastName;
			local.relationToClient = local.qryGetClientData.relationToClient;
			local.repAddress1 = local.qryGetClientData.repAddress1;
			local.repAddress2 = local.qryGetClientData.repAddress2;
			local.repCity = local.qryGetClientData.repCity;
			local.repState = local.qryGetClientData.repState;
			local.repPostalCode = local.qryGetClientData.repPostalCode;
			local.repEmail = local.qryGetClientData.repEmail;
			local.repHomePhone = local.qryGetClientData.repHomePhone;
			local.repCellPhone = local.qryGetClientData.repCellPhone;
			local.repAlternatePhone = local.qryGetClientData.repAlternatePhone;
			local.repHomePhoneE164 = local.qryGetClientData.repHomePhoneE164;
			local.repCellPhoneE164 = local.qryGetClientData.repCellPhoneE164;
			local.repAlternatePhoneE164 = local.qryGetClientData.repAlternatePhoneE164;
			local.repParentID = local.qryGetClientData.repParentID;	
			/* Referral Data */	
			local.clientReferralID = local.qryGetClientData.clientReferralID;
			local.importClientReferralID = local.qryGetClientData.importClientReferralID;
			local.sourceID = local.qryGetClientData.sourceID;
			local.feeTypeID = local.qryGetClientData.feeTypeID;
			local.otherSource = local.qryGetClientData.otherSource;
			local.enteredByMemberID = local.qryGetClientData.enteredByMemberID;
			if(not val(local.enteredByMemberID))
				local.enteredByMemberID = session.cfcuser.memberdata.memberID;
			local.qryGetMemberInfo = application.objMember.getMemberInfo(memberid=local.enteredByMemberID);
			local.memberID = local.qryGetClientData.memberID;
			local.qryGetMemberPhones = application.objMember.getMemberPhones(orgID=local.orgID, memberid=val(local.memberID));
			local.counselorName = local.qryGetMemberInfo.firstName & " " & local.qryGetMemberInfo.lastName;

			local.qryReferralNotes = this.objAdminReferrals.getReferralNotes(referralID=this.referralID,clientReferralID=local.clientReferralID);
			local.qryAllCounselorNotes = local.qryReferralNotes.filter(function(row){
				return (arguments.row.noteType eq 'C');
			});
			local.qryPendingCounselorNotes = local.qryReferralNotes.filter(function(row){
				return (arguments.row.noteType eq 'C' AND arguments.row.followUpStatus eq 'P');
			});
			
			local.typeID = local.qryGetClientData.referralTypeID;
			local.agencyID = local.qryGetClientData.agencyID;	
			local.agencyName = local.qryGetClientData.agencyName;	
			local.communicateLanguageID = local.qryGetClientData.communicateLanguageID;	
			local.issueDesc = local.qryGetClientData.issueDesc;	
			local.sendSurvey = arguments.event.getValue('sendSurvey',0);
			if(val(local.qryGetClientData.sendSurvey))
				local.sendSurvey = local.qryGetClientData.sendSurvey;
			local.sendNewsBlog = arguments.event.getValue('sendNewsBlog',0);
			if(val(local.qryGetClientData.sendNewsBlog))
				local.sendNewsBlog = local.qryGetClientData.sendNewsBlog;				
			local.statusID = local.qryGetClientData.statusID;
			local.statusName = local.qryGetClientData.statusName;
			local.canReferInd = local.qryGetClientData.canRefer;
			local.referralCanEditClient = local.qryGetClientData.referralCanEditClient;
			local.referralCanEditFilter = local.qryGetClientData.referralCanEditFilter;
			local.referralCanEditLawyer = local.qryGetClientData.referralCanEditLawyer;	
			local.isCoordination = local.qryGetClientData.isCoordination;
			local.isReferred = local.qryGetClientData.isReferred;
			local.isAgency = local.qryGetClientData.isAgency; 
			local.isPending	= local.qryGetClientData.isPending;
			local.isDeleted = local.qryGetClientData.isDeleted;
			local.isClosed = local.qryGetClientData.isClosed;
			local.isOpen = local.qryGetClientData.isOpen;
			local.clientReferralType = local.qryGetClientData.clientReferralType;
			local.isLawyerReferral = local.qryGetClientData.isLawyerReferral; 
			local.isAgencyReferral = local.qryGetClientData.isAgencyReferral; 
			local.memberName = local.qryGetClientData.memberName;
			local.callUID = local.qryGetClientData.callUID;
			local.labelTDwidth = "18%";
			local.clientReferralDate = local.qryGetClientData.clientReferralDate;
			local.dateCreated = local.qryGetClientData.dateCreated;
			local.callDate = local.qryGetClientData.callDate;
			local.dateLastUpdated = local.qryGetClientData.lastUpdatedDate;
			local.pendingClientAttorneyCoordination = local.isCoordination EQ 1 AND val(local.qryGetClientData.memberID);
			local.canViewAttorney = val(local.isReferred) OR local.pendingClientAttorneyCoordination;
			local.allowStatusUpdate = NOT val(local.isPending) AND NOT val(local.isClosed) AND NOT val(local.isDeleted);
			if (local.pendingClientAttorneyCoordination) {
				local.canReferInd = 0;
				local.allowStatusUpdate = true;
			}

			local.payLater = arguments.event.getValue('payLater',0);
			/* Case Data */
			local.caseID = local.qryGetClientData.caseID;
			local.caseStatusID = local.qryGetClientData.statusID; /* caseStatusID */
			local.caseFees = local.qryGetClientData.caseFees;
			local.dateCaseClosed = local.qryGetClientData.dateCaseClosed;
			local.dateCaseOpened = local.qryGetClientData.dateCaseOpened;
			local.dateCaseCreated = local.qryGetClientData.dateCaseCreated;	
			local.feeStructureID = local.qryGetClientData.feeStructureID;
			if (val(local.caseID)) local.allowStatusUpdate = true;
			/* Filter data */
			local.panelid1 = arguments.event.getValue('panelid1',0);
			local.subpanelid1 = arguments.event.getValue('subpanelid1',0);
			local.panelid2 = arguments.event.getValue('panelid2',0);
			local.subpanelid2 = arguments.event.getValue('subpanelid2',0);
			local.panelid3 = arguments.event.getValue('panelid3',0);
			local.subpanelid3 = arguments.event.getValue('subpanelid3',0);	
			/* Payment data */
			local.callPaymentProcessed = val(local.qryGetClientData.callPaymentProcessed); 
			if (currTab eq 'clientpayment' and not val(local.callPaymentProcessed) and local.clientPaymentSaleRecorded and not val(local.qryGetPaymentDataTotals.amtToBePaidTotal)){
				this.objAdminReferrals.updateClientPaymentStatus(clientID=local.clientID);
				local.callPaymentProcessed = 1; 
			}
			if (currTab eq 'clientpayment' and local.clientPaymentSaleRecorded and not val(local.qryGetPaymentDataTotals.amtToBePaidTotal)){
				local.sendReceiptEmail = this.objAdminReferrals.getPanelByID(local.panelID1).sendReceiptEmail;
				if(val(local.sendReceiptEmail))
					this.objAdminReferrals.doEmailClientReceipt(event=arguments.event);
			}			
			/* Lawyer data */
			local.lawyerPhone = local.qryGetMemberPhones.phone;
			local.memberIdList = arguments.event.getValue('selectedMemberIDs', valueList(local.qryGetReferralMembers.memberid));	
			local.clientRefTitle = "Client: " & local.qryGetClientData.firstName & ' ' & local.qryGetClientData.LastName;
			if (local.canViewAttorney)
				local.clientRefTitle = "Referral " & local.clientReferralID & " - " & local.clientName;
			if (val(local.dspImportClientReferralID) and len(trim(local.importClientReferralID)))
				local.clientRefTitle = local.clientRefTitle & "<br>Import Case " & trim(local.importClientReferralID);

			local.referralID = this.referralID;
			local.dspImportClientReferralID = this.dspImportClientReferralID;
			local.disableFilterTab = "true";
			local.disableLawyerTab = "true";
			local.qryStates = application.objCommon.getStates();
			local.tabLink = this.link.editClient;
			local.saveLink = this.link.editClient;
			local.formLink = this.link.editClient & "&tab=filters";
			local.cancelLink = this.link.manageClientReferral;
			local.resendEmailLink = this.link.resendReferralEmail;
			local.showResendEmailLink = this.link.showResendReferralEmail;

			local.feeDiscrepancyStatusID = local.qryGetClientData.feeDiscrepancyStatusID;
			if(len(trim(local.thiCFCMethod)) and local.thiCFCMethod neq "addClient")
				local.cancelLink = evaluate("this.link.#local.thiCFCMethod#");	
			if (arguments.event.valueExists('tab') and arguments.event.getValue('tab') eq "filters"){
				local.formLink = this.link.editClient & "&tab=lawyers";
				
			}
			if (arguments.event.valueExists('tab') and arguments.event.getValue('tab') eq "lawyers"){
				local.formLink = this.link.completeReferral;
				local.disableFilterTab = "false";
			}
			local.clientFieldNames = "FIRSTNAME,MIDDLENAME,LASTNAME,BUSINESSNAME,ADDRESS1,ADDRESS2,CITY,STATE,POSTALCODE,EMAIL,HOMEPHONE,HOMEPHONENATIONAL,HOMEPHONEE164,CELLPHONE,CELLPHONENATIONAL,CELLPHONEE164,ALTERNATEPHONE,ALTERNATEPHONENATIONAL,ALTERNATEPHONEE164,REPFIRSTNAME,REPLASTNAME,RELATIONTOCLIENT,REPADDRESS1,REPADDRESS2,REPCITY,REPSTATE,REPPOSTALCODE,REPEMAIL,REPHOMEPHONE,REPHOMEPHONENATIONAL,REPHOMEPHONEE164,REPCELLPHONE,REPCELLPHONENATIONAL,REPCELLPHONEE164,REPALTERNATEPHONE,REPALTERNATEPHONENATIONAL,REPALTERNATEPHONEE164,SOURCEID,OTHERSOURCE,COUNSELORNOTES,TYPEID,AGENCYID,AGENCYNAME,COMMUNICATELANGUAGEID,ISSUEDESC,SENDNEWSBLOG,SENDSURVEY#iif(not val(local.isPending) and not val(local.isClosed) and not val(local.isDeleted),DE(',STATUSID'),DE(''))#";					
			local.filterFieldNames = "panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3,prevPanelID";
			local.clientPaymentFieldNames = "";
			local.lawyerFieldNames = "SELECTEDMEMBERIDS";

			local.caseDocslink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralCaseDocuments&clientReferralID=#local.clientReferralID#&mode=stream";
			local.referralChangeHistorylink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralChangeHistory&clientReferralID=#local.clientReferralID#&mode=stream";
		</cfscript>
		
		<cfif listFindNoCase("filters,clientpayment,lawyers", currTab) and isDefined("form") and structIsEmpty(form)>
			<cfloop list="#local.clientFieldNames#" index="local.thisField">
				<cfif not structKeyExists(form,"#local.thisField#")>
					<cfset structInsert(form, "#local.thisField#", evaluate("local.qryGetClientData." & local.thisField)) />
				</cfif>
			</cfloop>		
		</cfif>
		
		<!--- Assign values read from XML Schema --->
		<cfloop query="local.qryGetReferralFilterData">		
			<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
				<cfcase value="panelid1">
					<cfset local.panelid1 =  local.qryGetReferralFilterData.elementValue />
					<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"panelid1")>
						<cfset structInsert(form, "panelid1", local.qryGetReferralFilterData.elementValue) />
					</cfif>							
				</cfcase>
				<cfcase value="subpanelid1">
					<cfset local.subpanelid1 =  local.qryGetReferralFilterData.elementValue />
					<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"subpanelid1")>
						<cfset structInsert(form, "subpanelid1", local.qryGetReferralFilterData.elementValue) />
					</cfif>							
				</cfcase>
				<cfcase value="panelid2">
					<cfset local.panelid2 =  local.qryGetReferralFilterData.elementValue />
					<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"panelid2")>
						<cfset structInsert(form, "panelid2", local.qryGetReferralFilterData.elementValue) />
					</cfif>							
				</cfcase>
				<cfcase value="subpanelid2">
					<cfset local.subpanelid2 =  local.qryGetReferralFilterData.elementValue />
					<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"subpanelid2")>
						<cfset structInsert(form, "subpanelid2", local.qryGetReferralFilterData.elementValue) />
					</cfif>							
				</cfcase>
				<cfcase value="panelid3">
					<cfset local.panelid3 =  local.qryGetReferralFilterData.elementValue />
					<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"panelid3")>
						<cfset structInsert(form, "panelid3", local.qryGetReferralFilterData.elementValue) />
					</cfif>							
				</cfcase>
				<cfcase value="subpanelid3">
					<cfset local.subpanelid3 =  local.qryGetReferralFilterData.elementValue />
					<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"subpanelid3")>
						<cfset structInsert(form, "subpanelid3", local.qryGetReferralFilterData.elementValue) />
					</cfif>							
				</cfcase>																				
			</cfswitch>		
		</cfloop>		
		
		<!--- Initialize local variables and append custom field names to field names list --->
		<cfif isDefined("local.xmlFields.xmlRoot.xmlChildren") and arrayLen(local.xmlFields.xmlRoot.xmlChildren)>	
			<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
				<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = arguments.event.getValue('#local.thisfield.xmlattributes.fieldCode#','') />
				<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"#local.thisfield.xmlattributes.fieldCode#")>
					<cfset structInsert(form, "#local.thisfield.xmlattributes.fieldCode#", arguments.event.getValue('#local.thisfield.xmlattributes.fieldCode#','')) />
				</cfif>	
				<cfloop query="local.qryGetReferralFilterData">		
					<cfif local.qryGetReferralFilterData.elementID eq local.thisfield.xmlattributes.fieldCode>
						<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = local.qryGetReferralFilterData.elementValue />
						<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not len(trim(evaluate("form.#local.thisfield.xmlattributes.fieldCode#")))>
							<cfset "form.#local.thisfield.xmlattributes.fieldCode#" = local.qryGetReferralFilterData.elementValue />
						</cfif>
						<cfbreak />
					</cfif>
				</cfloop>
				<cfset local.filterFieldNames = listAppend(local.filterFieldNames,local.thisfield.xmlattributes.fieldCode) />
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>
					<cfset local.filterFieldNames = listAppend(local.filterFieldNames,"#local.thisfield.xmlattributes.fieldCode#_radius") />
					<cfset "local.#local.thisfield.xmlattributes.fieldCode#_radius" = arguments.event.getValue('#local.thisfield.xmlattributes.fieldCode#_radius','') />						
					<cfloop query="local.qryGetReferralFilterData">		
						<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID) >
							<cfset "local.#local.thisfield.xmlattributes.fieldCode#_radius" = local.qryGetReferralFilterData.elementValue />
							<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"#local.thisfield.xmlattributes.fieldCode#_radius")>
								<cfset structInsert(form, "#local.thisfield.xmlattributes.fieldCode#_radius", local.qryGetReferralFilterData.elementValue) />
							</cfif>
							<cfbreak />
						</cfif>
					</cfloop>
					<cfif local.postalCode NEQ "">
						<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = local.postalCode>
					<cfelseif local.repPostalCode NEQ "">
						<cfset "local.#local.thisfield.xmlattributes.fieldCode#" = local.repPostalCode>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>	

		<!--- Initialize local variables and append classification fields to field names list --->
		<cfloop query="local.qryGetClassifications">		
			<cfif val(local.qryGetClassifications.allowSearch)>
				<cfset local.filterFieldNames = listAppend(local.filterFieldNames,"mg_gid_#local.qryGetClassifications.groupSetID#") />
				<cfset "local.mg_gid_#local.qryGetClassifications.groupSetID#" = arguments.event.getValue('mg_gid_#local.qryGetClassifications.groupSetID#','') />
				<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not structKeyExists(form,"mg_gid_#local.qryGetClassifications.groupSetID#")>
					<cfset structInsert(form, "mg_gid_#local.qryGetClassifications.groupSetID#", arguments.event.getValue('mg_gid_#local.qryGetClassifications.groupSetID#','')) />
				</cfif>
				<cfloop query="local.qryGetReferralFilterData">		
					<cfif local.qryGetReferralFilterData.elementID eq "mg_gid_#local.qryGetClassifications.groupSetID#">
						<cfset "local.mg_gid_#local.qryGetClassifications.groupSetID#" = local.qryGetReferralFilterData.elementValue />
						<cfif listFindNoCase("client,clientpayment,lawyers", currTab) and isDefined("form") and not len(trim(evaluate("form.mg_gid_#local.qryGetClassifications.groupSetID#")))>
							<cfset "form.mg_gid_#local.qryGetClassifications.groupSetID#" = local.qryGetReferralFilterData.elementValue />
						</cfif>							
						<cfbreak />	
					</cfif>					
				</cfloop>
			</cfif>
		</cfloop>	
		
		<cfset local.referralFeePercent = 0 />
		<cfset local.clientReferralAmount = 0 />
		<cfset local.skipClientPayment = 0>
		<cfset local.primPanelName = "" />
		<cfset local.defaultCallType = local.referralSettingsQry.defaultCallTypeId>
		<cfset local.defaultClientSurvey = local.referralSettingsQry.defaultClientSurvey>
		<cfif val(local.panelid1)>
			<cfset local.qryGetPrimPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.panelid1) />
			<cfset local.referralFeePercent = local.qryGetPrimPanelInfo.referralFeePercent />
			<cfset local.clientReferralAmount = local.qryGetPrimPanelInfo.clientReferralAmount />
			<cfset local.clientFeeGLAccountID = iif(val(local.qryGetPrimPanelInfo.clientFeeGLAccountID), local.qryGetPrimPanelInfo.clientFeeGLAccountID , this.GLAccountID) />
			<cfset local.primPanelName = local.qryGetPrimPanelInfo.name />
		</cfif>	

		<cfif val(local.panelid1) and not val(local.clientReferralAmount)>
			<cfset local.skipClientPayment = 1>
		</cfif>

		<cfif val(local.caseID)>
			<cfset local.qryGetCaseFeeStructureLevels = local.objReferrals.getCaseFeeStructureLevelsByID(feeStructureID=val(local.feeStructureID)) />
			<cfif local.allowFeeDiscrepancy>
				<cfset local.qryGetFeeDiscrepancyStatuses = this.objAdminReferrals.getFeeDiscrepancyStatuses(isAdmin=1)>
				<cfset local.qryGetFeeDiscrepancyStatusChangeLog = this.objAdminReferrals.getFeeDiscrepancyStatusChangeLog(clientReferralID=local.clientReferralID)>
			</cfif>
		</cfif>
		<cfset local.questionAnswerPath = this.objAdminReferrals.getSearchXMLByClientID(local.clientID)>

		<cfif not local.isPrinterFiendly>
			<!--- get the SRID and permissions of TransactionsAdmin. --->
			<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
			<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

            <cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>

			<cfif (currTab eq 'clientpayment' OR currTab eq 'client') and local.clientPaymentSaleRecorded and val(local.qryGetPaymentDataTotals.amtToBePaidTotal)>					
				<cfset local.actorMemberID = this.clientFeeMemberID>
				<cfif local.qryGetPaymentData.itemType IS 'ClientReferralFee'>
					<cfset local.actorMemberID = local.qryGetPaymentData.assignedToMemberID>
				</cfif>
				<cfset local.addClientPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.actorMemberID, t="Client Referral Fee | #local.clientName# | #local.primPanelName# | #local.callUID#", ta=local.qryGetPaymentDataTotals.amtToBePaidTotal, tmid=local.actorMemberID, ad="s|#valueList(local.qryGetPaymentData.saleTID)#", xcof=1, lckm=1, ocid=local.clientID)>
			</cfif>

			<cfset local.hasConsultationFees = false>
			<cfif currTab eq 'client' AND this.collectClientFeeFE AND local.canViewAttorney>
				<cfset local.qryConsultationFees = this.objAdminReferrals.getConsultationFees(clientReferralID=local.clientReferralID, 
						orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberid=val(local.qryGetClientData.memberID))>
				<cfif local.qryConsultationFees.recordCount>
					<cfset local.hasConsultationFees = true>

					<cfquery name="local.qryConsultationFeeTotals" dbtype="query">
						select sum(referralDues) as referralDuesTotal, sum(amtToBePaid) as amtToBePaidTotal, sum(paidToDate) as paidToDateTotal
						from [local].qryConsultationFees
					</cfquery>
				</cfif>
			</cfif>
			
			<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
			<cfset local.extraInformation = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				viewMode='bs4', resourceType='ClientReferrals', areaName='ClientReferrals', csrid=this.siteResourceID, detailID=0, hideAdminOnly=0, itemType='ClientRefCustom', 
				itemID=local.clientReferralID, trItemType='', trApplicationType='')>

			<cfif local.canViewAttorney>
				<cfset local.strAttorneyFields = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
					viewMode='bs4', resourceType='Referrals', areaName='Attorney', csrid=this.siteResourceID, detailID=0, hideAdminOnly=0, itemType='AttorneyCustom', 
					itemID=local.clientReferralID, trItemType='', trApplicationType='')>
			</cfif>

			<cfif currTab eq 'lawyers' OR currTab eq 'clientpayment'>
				<cfif local.paging.currpage eq 1>
					<cfset local.currCountStart = 1>
					<cfset local.currCountStop 	= local.paging.rowsize>
				<cfelse>
					<cfset local.currCountStart = local.paging.rowSize * (local.paging.currpage - 1) + 1>
					<cfset local.currCountStop 	= local.paging.rowsize * (local.paging.currpage - 1) + local.qryGetSearchResults.recordCount>
				</cfif>
			</cfif>	

			<cfset appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(replaceNoCase(local.clientRefTitle,'<br>',' - ')) })>
			
			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfinclude template="frm_clientReferral.cfm">
				</cfoutput>
			</cfsavecontent>
			
			<cfstoredproc procedure="ref_updateViewedClientTime" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.clientID#" />
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberLoggedInID#" />
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.siteID#" />
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.orgID#" />
			</cfstoredproc>
		<cfelse>
			<cfset local.qryClientReferralCustomFieldData = local.objReferrals.getClientReferralCustomFieldData(clientReferralID=local.clientReferralID)>
			<cfset local.returnPDFStruct = doGenerateRefPrinterFriendly(event=arguments.event, refStruct=local) />
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnPDFStruct.referralFilePath, displayName=ListLast(local.returnPDFStruct.referralFilePath,"/"), deleteSourceFile=1)>
			<cfif not local.docResult>
				<cfsavecontent variable="local.data">
					<cfoutput>	
						<script>
						top.listReferrals();	
						</script>						
					</cfoutput>
				</cfsavecontent>			
			</cfif>	

		</cfif>
			
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="saveClient" access="public" output="false" returntype="numeric" hint="This function either inserts or updates referral data">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();		
				
			local.clientReferralID = val(arguments.event.getValue('clientReferralID'));			
			arguments.event.setValue('clientTypeID',1);
			local.caseID = val(arguments.event.getValue('caseID'));
			if (local.clientReferralID){			
				this.objAdminReferrals.updateClientReferral(arguments.event);
				if (local.caseID)
					this.objAdminReferrals.updateClientCase(arguments.event);	
			}
			else
				local.clientReferralID = this.objAdminReferrals.insertClientReferral(arguments.event);
		</cfscript>

		<cfreturn local.clientReferralID />
	</cffunction>

	<cffunction name="getPanelReferralData" access="public" output="false" returntype="string" hint="This returns a string with panels selected in the search filter">
		<cfargument name="clientID" type="numeric">
		<cfargument name="panelStr" type="struct">

		<cfset var local = structNew()>

		<cfset local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=arguments.clientID)>
		<cfset local.qryGetPanels = this.objAdminReferrals.getPanels(referralID=this.referralID)>
		<cfset local.panelid1  = arguments.panelStr.panelid1>
		<cfset local.panelid2  = arguments.panelStr.panelid2>
		<cfset local.panelid3  = arguments.panelStr.panelid3>
		<cfset local.panelid1Name  = "">
		<cfset local.panelid2Name  = "">
		<cfset local.panelid3Name  = "">	
		
		<cfloop query="local.qryGetPanels">
			<cfif local.panelid1 is local.qryGetPanels.panelID><cfset local.panelid1Name  = local.qryGetPanels.name>cfbreak /></cfif>
		</cfloop>	
		<cfset local.subpanelid1 = "" />
		<cfset local.subpanelid1List = "" />
		<cfloop query="local.qryGetReferralFilterData">
			<cfif listFindNoCase("subpanelid1",local.qryGetReferralFilterData.elementID)>
				<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
					<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
					<cfif not listFindNoCase(local.subpanelid1, local.qryGetPanelInfo.name)>
						<cfset local.subpanelid1List = listAppend(local.subpanelid1List,local.thisPanelID) />
						<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
					</cfif>							
				</cfloop>
			</cfif>
		</cfloop>	
		<cfloop query="local.qryGetPanels">
			<cfif local.panelid2 is local.qryGetPanels.panelID><cfset local.panelid2Name  = local.qryGetPanels.name><cfbreak /></cfif>
		</cfloop>
		<cfset local.subpanelid2 = "" />
		<cfset local.subpanelid2List = "" />
		<cfloop query="local.qryGetReferralFilterData">
			<cfif listFindNoCase("subpanelid2",local.qryGetReferralFilterData.elementID)>
				<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
					<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
					<cfif not listFindNoCase(local.subpanelid2, local.qryGetPanelInfo.name)>
						<cfset local.subpanelid2List = listAppend(local.subpanelid2List,local.thisPanelID) />
						<cfset local.subpanelid2 = listAppend(local.subpanelid2,local.qryGetPanelInfo.name) />
					</cfif>
				</cfloop>
			</cfif>
		</cfloop>
		<cfloop query="local.qryGetPanels">
			<cfif local.panelid3 is local.qryGetPanels.panelID><cfset local.panelid3Name  = local.qryGetPanels.name><cfbreak /></cfif>
		</cfloop>
		<cfset local.subpanelid3 = "" />
		<cfset local.subpanelid3List = "" />
		<cfloop query="local.qryGetReferralFilterData">
			<cfif listFindNoCase("subpanelid3",local.qryGetReferralFilterData.elementID)>
				<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
					<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
					<cfif not listFindNoCase(local.subpanelid3, local.qryGetPanelInfo.name)>
						<cfset local.subpanelid3List = listAppend(local.subpanelid3List,local.thisPanelID) />
						<cfset local.subpanelid3 = listAppend(local.subpanelid3,local.qryGetPanelInfo.name) />
					</cfif>
				</cfloop>
			</cfif>
		</cfloop>							

		<cfsavecontent  variable="local.thisPanelData">
		<cfoutput>*** PANEL INFORMATION ***
			Primary Panel: #local.panelid1Name#
				Sub-Panel: <cfif len(local.subpanelid1)>#local.subpanelid1#<cfelse>N/A</cfif>
			<cfif len(local.panelid2Name)>Secondary Panel: #local.panelid2Name#
				Sub-Panel: <cfif len(local.subpanelid2)>#local.subpanelid2#<cfelse>N/A</cfif></cfif>
			<cfif len(local.panelid3Name)>Tertiary Panel: #local.panelid3Name#
				Sub-Panel: <cfif len(local.subpanelid3)>#local.subpanelid3#<cfelse>N/A</cfif></cfif>				
		</cfoutput>
		</cfsavecontent>		

		<cfreturn local.thisPanelData />
	</cffunction>	

	<cffunction name="adjustTransaction" access="public" output="false" returntype="struct" hint="adjust fees collected from client">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.caseID = val(arguments.event.getValue('cid'));
			local.collectedFeeID = val(arguments.event.getValue('cfid'));
			local.memberID = val(arguments.event.getValue('mid'));
			local.saleTIDList = arguments.event.getValue('tidlist');
			local.panelID = val(arguments.event.getValue('pid'));
			local.clientID = val(arguments.event.getValue('clid'));
			local.clientParentID = val(arguments.event.getValue('clpid'));					
			local.formlink = this.link.saveAdjustTransaction;			
			local.qryGetTransaction = this.objAdminReferrals.getCaseFees(caseID=val(local.caseID), collectedFeeID=val(local.collectedFeeID));
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_adjustTransaction.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveAdjustTransaction" access="public" output="false" returntype="struct" hint="adjust fees collected from client">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objReferrals	= CreateObject("component","model.referrals.referrals");
			local.referralID = this.referralID;
			local.caseID = val(arguments.event.getValue('cid'));
			local.collectedFeeID = val(arguments.event.getValue('cfid'));
			local.memberID = val(arguments.event.getValue('mid'));
			local.saleTIDList = arguments.event.getValue('tidlist');
			local.panelID = val(arguments.event.getValue('pid'));
			local.clientID = val(arguments.event.getValue('clid'));
			local.clientParentID = val(arguments.event.getValue('clpid'));				
			local.referralFeePercent = val(arguments.event.getValue('referralFeePercent'));
			local.originalCollectedFeeAmount = val(arguments.event.getValue('originalAmount'));
			local.newCollectedFeeAmount = val(arguments.event.getValue('amount'));
			local.newSaleAmount = 0;

			local.feesStruct = structNew();
			local.feesStruct = local.objReferrals.getPanelFees(memberID=local.memberID, clientID=local.clientID, clientParentID=local.clientParentID);
			local.referralFeePercent = local.feesStruct.referralFeePercent;
			local.referralAmount = local.feesStruct.referralAmount;
			local.collectedFee = local.newCollectedFeeAmount;
			local.qryGetCaseData = local.objReferrals.getCaseData(local.caseID);
			local.qryGetPanelData = this.objAdminReferrals.getPanelByID(panelID=local.panelID);

			if (val(local.newCollectedFeeAmount)) {
				local.newSaleAmount = local.objReferrals.calculateFees(referralID=local.referralID, panelID=local.panelID, caseID=local.caseID, collectedFee=local.collectedFee, 
										qryGetCaseData=local.qryGetCaseData, qryGetPanelData=local.qryGetPanelData);
			}

			local.adjustedSuccess = 1;
		</cfscript>

		<cftry>
			<cfquery name="local.qryAdjust" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @nowDate datetime, @recordedByMemberID int, @statsSessionID int, @minSaleTID int, 
						@invoiceProfileID int, @invoiceID int, @invoiceNumber varchar(19), @trashID int, @collectedFeeID int, 
						@newCollectedFee decimal(18,2), @totalOldFee decimal(18,2), @newFee decimal(18,2), @thisFee decimal(18,2),
						@saleAmount decimal(18,2), @adjAmount decimal(18,2), @assignedToMemberID int, @invoiceIDList varchar(max),
						@leftAdjAmount decimal(18,2), @caseID int, @saleTIDList varchar(max), @orgID int;
					DECLARE @tblSaleDetails TABLE (saleTID int, saleAmount decimal(18,2));
					DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int);
					
					set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					set @nowDate = getdate();
					set @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;
					set @statsSessionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.statsSessionID#">;
					set @assignedToMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberID#">;
					set @collectedFeeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.collectedFeeID)#">;
					set @newCollectedFee = <cfqueryparam cfsqltype="cf_sql_decimal" scale="2" value="#local.newCollectedFeeAmount#">;
					set @newFee = <cfqueryparam cfsqltype="cf_sql_decimal" scale="2" value="#local.newSaleAmount#">;
					set @caseID = <cfqueryparam value="#val(local.caseID)#" cfsqltype="cf_sql_integer">;
					set @saleTIDList = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.saleTIDList#">;
					select @orgID = orgID from dbo.sites where siteID = @siteID;

					INSERT INTO @tblSaleDetails (saleTID, saleAmount)
					select tbl.listitem, ts.cache_amountAfterAdjustment
					from dbo.fn_intListToTable(@saleTIDList,',') as tbl
					inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tbl.listitem;

					select @totalOldFee = sum(saleAmount) from @tblSaleDetails;

					BEGIN TRAN;
						-- Update collected fees
						update dbo.ref_collectedFees
						set collectedFee = @newCollectedFee
						where caseID = @caseID
						and collectedFeeID = @collectedFeeID;

						IF @totalOldFee > @newFee BEGIN
							SET @leftAdjAmount = @totalOldFee - @newFee;

							select @minSaleTID = min(saleTID) from @tblSaleDetails;
							WHILE @minSaleTID IS NOT NULL BEGIN
								select @invoiceProfileID=null, @invoiceID=null, @invoiceNumber=null, @saleAmount=null, @adjAmount=null;

								select @saleAmount = saleAmount
								from @tblSaleDetails
								where saleTID = @minSaleTID;

								select @invoiceProfileID = i.invoiceProfileID
									from dbo.tr_invoiceTransactions it
									inner join dbo.tr_invoices i on i.orgID = @orgID and i.invoiceID = it.invoiceID
									where it.orgID = @orgID 
									and it.transactionID = @minSaleTID;
								select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

								IF @invoiceID is null BEGIN
									EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
										@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, 
										@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

									insert into @tblInvoices (invoiceID, invoiceProfileID)
									values (@invoiceID, @invoiceProfileID);
								END

								-- select minimum of saleAmount and amount left to be adjusted
								SELECT @adjAmount=MIN(x) FROM (VALUES (@saleAmount),(@leftAdjAmount)) AS value(x);

								SET @leftAdjAmount = @leftAdjAmount - @adjAmount;
								SET @adjAmount = @adjAmount * -1;

								EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
									@statsSessionID=@statsSessionID, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowDate, 
									@autoAdjustTransactionDate=1, @saleTransactionID=@minSaleTID, @invoiceID=@invoiceID, @byPassTax=0, 
									@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;

								-- if adjustment amount covered, stop the loop.
								if @leftAdjAmount = 0
									break;

								select @minSaleTID = min(saleTID) 
								from @tblSaleDetails 
								where saleTID > @minSaleTID;
							END
						END

						select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) from @tblInvoices;
						IF len(@invoiceIDList) > 0
							EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceIDList;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfset local.adjustedSuccess = 0>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfif local.adjustedSuccess>
			<cfset local.strResponse.success = true>
			<cfset createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')),
				clientReferralID=int(local.qryGetCaseData.clientReferralID), actorMemberID=int(session.cfcuser.memberdata.memberid), 
				mainMessage="Attorney Sale Edited - #dollarFormat(local.newSaleAmount)#")>			
		</cfif>		

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadCasePage();
				top.closeBox();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>	

	<cffunction name="listReferrals" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.qryGetClientsViewed = this.objAdminReferrals.getClientsViewed(referralID=this.referralID,memberid=session.cfcuser.memberdata.memberID);
			
			local.qryGetReferralStatus = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID).qryStatus;

			if(this.allowFeeDiscrepancy)
				local.qryGetFeeDiscrepancyStatuses = this.objAdminReferrals.getFeeDiscrepancyStatuses();
			local.qryGetInterviewers = this.objAdminReferrals.getInterviewers(referralID=this.referralID,orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			local.urlString = "";
			local.referralListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferrals&referralID=#this.referralID#&mode=stream";
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit');
			arguments.event.paramValue('referralDateFrom',dateFormat(now(),"m/d/yyyy"));
			arguments.event.paramValue('transferDateFrom',dateFormat(dateadd("d",-30,now()),"m/d/yyyy"));
		</cfscript>

		<cfset local.firstName = ''>
		<cfset local.lastName = ''>
		<cfset local.email = ''>
		<cfset local.repFirstName = ''>
		<cfset local.repLastName = ''>
		<cfset local.memberFirstName = ''>
		<cfset local.memberLastName = ''>
		<cfset local.phoneNumber = ''>
		<cfset local.referralNum = ''>
		<cfset local.statusID = 0>
		<cfset local.counselorID = ''>
		<cfset local.importClientReferralID = ''>
		<cfset local.referralDateFrom = ''>
		<cfset local.referralDateTo = ''>
		<cfset local.callDateFrom = ''>
		<cfset local.callDateTo = ''>
		<cfset local.noteFollowUpDateFrom = ''>
		<cfset local.noteFollowUpDateTo = ''>
		<cfset local.followUpStatus = ''>
		<cfset local.callUID = ''>
		<cfset local.isTransferred = 0>
		<cfset local.transferDateFrom =  arguments.event.getValue('transferDateFrom')>
		<cfset local.transferDateTo = ''>
		<cfset local.hasDocuments = 0>
		<cfset local.feeDiscrepancyStatusID = 0>
		<cfset local.referralFilter = application.mcCacheManager.sessionGetValue(keyname='referralFilter', defaultValue={})>
		<cfif local.referralFilter.count()>
			<cfset local.firstName = local.referralFilter.firstName>
			<cfset local.lastName = local.referralFilter.lastName>
			<cfset local.email = structKeyExists(local.referralFilter,"email") ? local.referralFilter.email : ''>
			<cfset local.repFirstName = structKeyExists(local.referralFilter,"repFirstName") ? local.referralFilter.repFirstName : ''>
			<cfset local.repLastName = structKeyExists(local.referralFilter,"repLastName") ? local.referralFilter.repLastName : ''>
			<cfset local.memberFirstName = local.referralFilter.memberFirstName>
			<cfset local.memberLastName = local.referralFilter.memberLastName>
			<cfset local.phoneNumber = local.referralFilter.phoneNumber>
			<cfset local.referralNum = local.referralFilter.referralNum>
			<cfset local.statusID = local.referralFilter.statusID>
			<cfset local.counselorID = local.referralFilter.counselorID>
			<cfset local.importClientReferralID = local.referralFilter.importClientReferralID>
			<cfset local.referralDateFrom = local.referralFilter.referralDateFrom>
			<cfset local.referralDateTo = local.referralFilter.referralDateTo>
			<cfset local.callDateFrom = local.referralFilter.callDateFrom>
			<cfset local.callDateTo = local.referralFilter.callDateTo>
			<cfif structKeyExists(local.referralFilter,"noteFollowUpDateFrom")>
				<cfset local.noteFollowUpDateFrom = local.referralFilter.noteFollowUpDateFrom>
			<cfelse>
				<cfset local.noteFollowUpDateFrom = ''>
			</cfif>
			<cfif structKeyExists(local.referralFilter,"noteFollowUpDateTo")>
				<cfset local.noteFollowUpDateTo = local.referralFilter.noteFollowUpDateTo>
			<cfelse>
				<cfset local.noteFollowUpDateTo = ''>
			</cfif>
			<cfif structKeyExists(local.referralFilter,"followUpStatus")>
				<cfset local.followUpStatus = local.referralFilter.followUpStatus>
			</cfif>
			<cfset local.callUID = local.referralFilter.callUID>
			<cfset local.isTransferred = local.referralFilter.isTransferred>
			<cfif local.isTransferred  eq 1>					
				<cfset local.transferDateFrom = local.referralFilter.transferDateFrom>
				<cfset local.transferDateTo = local.referralFilter.transferDateTo>
			</cfif>
			<cfif StructKeyExists(local.referralFilter,"hasDocuments")>
				<cfset local.hasDocuments = local.referralFilter.hasDocuments>
			</cfif>
			<cfif StructKeyExists(local.referralFilter,"feeDiscrepancyStatusID")>
				<cfset local.feeDiscrepancyStatusID = local.referralFilter.feeDiscrepancyStatusID>
			</cfif>
		</cfif>		
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_referralManagement.cfm" />		
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	<cffunction name="listRetainedCases" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />

		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.qryGetClientsViewed = this.objAdminReferrals.getClientsViewed(referralID=this.referralID,memberid=session.cfcuser.memberdata.memberID);
			
			local.qryGetCaseStatuses = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID).qryStatus;

			local.qryGetInterviewers = this.objAdminReferrals.getInterviewers(referralID=this.referralID,orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			if(this.allowFeeDiscrepancy)
				local.qryGetFeeDiscrepancyStatuses = this.objAdminReferrals.getFeeDiscrepancyStatuses();
			local.editPanelMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit') & "&tab=referral";
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit');	
			local.urlString = "";

			local.caseListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getCases&referralID=#this.referralID#&mode=stream";
			arguments.event.paramValue('referralDateFrom',dateFormat(now(),"m/d/yyyy"));
		</cfscript>		

		<cfset local.firstName = ''>
		<cfset local.lastName = ''>
		<cfset local.email = ''>
		<cfset local.repFirstName = ''>
		<cfset local.repLastName = ''>
		<cfset local.memberFirstName = ''>
		<cfset local.memberLastName = ''>
		<cfset local.referralDateFrom = arguments.event.getValue('referralDateFrom')>
		<cfset local.referralDateTo = ''>
		<cfset local.caseDateFrom = ''>
		<cfset local.caseDateTo = ''>
		<cfset local.noteFollowUpDateFrom = ''>
		<cfset local.noteFollowUpDateTo = ''>
		<cfset local.statusID = 0>
		<cfset local.hasDocuments = 0>
		<cfset local.followUpStatus = ''>
		<cfset local.feeDiscrepancyStatusID = 0>
		<cfset local.counselorID = ''>
		<cfset local.retainedCaseFilter = application.mcCacheManager.sessionGetValue(keyname='retainedCaseFilter', defaultValue={})>
		<cfif local.retainedCaseFilter.count()>
			<cfset local.firstName = local.retainedCaseFilter.firstName>
			<cfset local.lastName = local.retainedCaseFilter.lastName>
			<cfset local.email = structKeyExists(local.retainedCaseFilter,"email") ? local.retainedCaseFilter.email : ''>
			<cfset local.repFirstName = structKeyExists(local.retainedCaseFilter,"repFirstName") ? local.retainedCaseFilter.repFirstName : ''>
			<cfset local.repLastName = structKeyExists(local.retainedCaseFilter,"repLastName") ? local.retainedCaseFilter.repLastName : ''>
			<cfset local.memberFirstName = local.retainedCaseFilter.memberFirstName>
			<cfset local.memberLastName = local.retainedCaseFilter.memberLastName>
			<cfset local.referralDateFrom = local.retainedCaseFilter.referralDateFrom>
			<cfset local.referralDateTo = local.retainedCaseFilter.referralDateTo>
			<cfset local.caseDateFrom = local.retainedCaseFilter.caseDateFrom>
			<cfset local.caseDateTo = local.retainedCaseFilter.caseDateTo>
			<cfset local.statusID = local.retainedCaseFilter.statusID>
			<cfif StructKeyExists(local.retainedCaseFilter,"hasDocuments")>
				<cfset local.hasDocuments = local.retainedCaseFilter.hasDocuments>
			<cfelse>
				<cfset local.hasDocuments = 0>
			</cfif>
			<cfif structKeyExists(local.retainedCaseFilter,"noteFollowUpDateFrom")>
				<cfset local.noteFollowUpDateFrom = local.retainedCaseFilter.noteFollowUpDateFrom>
			<cfelse>
				<cfset local.noteFollowUpDateFrom = ''>
			</cfif>
			<cfif structKeyExists(local.retainedCaseFilter,"noteFollowUpDateTo")>
				<cfset local.noteFollowUpDateTo = local.retainedCaseFilter.noteFollowUpDateTo>
			<cfelse>
				<cfset local.noteFollowUpDateTo = ''>
			</cfif>
			<cfif structKeyExists(local.retainedCaseFilter,"followUpStatus")>
				<cfset local.followUpStatus = local.retainedCaseFilter.followUpStatus>
			</cfif>
			<cfif StructKeyExists(local.retainedCaseFilter,"feeDiscrepancyStatusID")>
				<cfset local.feeDiscrepancyStatusID = local.retainedCaseFilter.feeDiscrepancyStatusID>
			</cfif>
			<cfif StructKeyExists(local.retainedCaseFilter,"counselorID")>
				<cfset local.counselorID = local.retainedCaseFilter.counselorID>
			</cfif>
		</cfif>		
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_caseManagement.cfm" />		
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo") >
	</cffunction>	
	
	<cffunction name="completeReferral" access="public" output="false" returntype="Struct" hint="Complete referral process by counselor">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.referralTypeIsAgency = this.objAdminReferrals.getReferralTypeByID(typeID=arguments.event.getValue('typeID')).isAgency;
			local.clientReferralID = arguments.event.getValue('clientReferralID');
			local.memberIdList = "";
			if(arguments.event.valueExists('selectedMemberIDs') and listLen(arguments.event.getValue('selectedMemberIDs')))
				local.memberIdList =  arguments.event.getValue('selectedMemberIDs');
		</cfscript>

		<cfset local.clientReferralID = saveClient(event=arguments.event) />
		<cfif val(local.clientReferralID)>
			<cfset local.qryGetClientData = this.objAdminReferrals.getClient(clientReferralID=local.clientReferralID) />
			<cfset local.callUID = local.qryGetClientData.callUID />
			<cfset local.clientID = local.qryGetClientData.clientID>
			<cfset local.isCoordination = local.qryGetClientData.isCoordination EQ 1>
			<cfset this.objAdminReferrals.updateClientReferralDate(clientReferralID=local.clientReferralID) />
			<cfif local.referralTypeIsAgency>
				<cfset local.statusID = 0>
				<cfscript>
				local.referralStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(isAgency=1, referralID=arguments.event.getValue('referralID'));
				if(local.referralStatusResponseStruct.success){
					local.statusID = local.referralStatusResponseStruct.qryStatus.clientReferralStatusID;
				} else {
					return showRefError(msg='Process could not be completed as referral statuses for Agency is not defined correctly. Please contact administrator.');
				}
				</cfscript>
				<cfset this.objAdminReferrals.updateClientReferralStatus(clientReferralID=local.clientReferralID, statusID=local.statusID) />
				<cfset local.emailAgencyInfoToClient = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).emailAgencyInfoToClient />
				<cfif val(local.emailAgencyInfoToClient) and arguments.event.getValue('sendAgencyInfo','0')>
					<cfset doEmailAgencyInfo(event=arguments.event, callUID=local.callUID) />
				</cfif>
				<cfset local.confMessage = "A referral to Agency record has successfully been created." />
			<cfelse>
				<cfif not val(this.dspLegalDescription) and not find("*** PANEL INFORMATION ***", arguments.event.getValue('issueDesc'))>
					<cfset local.panelStr = structNew()>
					<cfset local.panelStr.panelid1 = val(arguments.event.getValue('panelid1',''))>
					<cfset local.panelStr.panelid2 = val(arguments.event.getValue('panelid2',''))>
					<cfset local.panelStr.panelid3 = val(arguments.event.getValue('panelid3',''))>					
					<cfset local.tempIssueDesc = arguments.event.getValue('issueDesc') & chr(10) & getPanelReferralData(clientID=local.clientID, panelStr=local.panelStr)>
					<cfset this.objAdminReferrals.updateIssueDesc(clientReferralID=local.clientReferralID, issueDesc=local.tempIssueDesc) />
				</cfif>			
				<cfif listLen(local.memberIdList)>
					<!--- update to "Pending - Referral Sent" if not in co-ordination status --->
					<cfif NOT local.isCoordination>
						<cfset local.statusID = 0 >	
						<cfscript>
						local.referralStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(isReferred=1, isRetainedCase=0, isOpen=1, referralID=arguments.event.getValue('referralID'), isActive=1);
						if(local.referralStatusResponseStruct.success){
							local.statusID = local.referralStatusResponseStruct.qryStatus.clientReferralStatusID;
						} else {
							return showRefError(msg='Process could not be completed as referral statuses with open and referred status is not defined. Please contact administator.');
						}
						</cfscript>
						
						<cfset this.objAdminReferrals.updateClientReferralStatus(clientReferralID=local.clientReferralID, statusID=local.statusID)>
					</cfif>

					<cftry>
						<cfset this.objAdminReferrals.updateMemberClientReferral(clientReferralID=local.clientReferralID, memberID=listGetAt(local.memberIdList,1)) />
						<cfcatch type="any">
							<cfthrow message="Failed to update referral - memberID" />
						</cfcatch>
					</cftry>

					<cftry>
						<cfset local.memberIdList = listDeleteAt(local.memberIdList,1) />
						<cfcatch type="any">
							<cfset local.memberIdList = "" />
						</cfcatch>
					</cftry>

					<!--- Insert loop and clientReferral Inserts --->
					<cfloop list="#local.memberIdList#" index="local.thisMemberID">
						<cfset local.copyClientReferralID = this.objAdminReferrals.copyClientReferral(clientReferralID=local.clientReferralID, memberID=local.thisMemberID) />

						<cfset createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')),clientReferralID=int(local.copyClientReferralID), actorMemberID=int(arguments.event.getValue('memberLoggedInID')), mainMessage="Referral Created")>	
					</cfloop>

					<cfif local.isCoordination>
						<cfset local.sendEmails = sendReferralCoordinationEmails(event=arguments.event, callUID=local.callUID)>
						<cfset local.confMessage = "The information has been saved.#local.sendEmails ? ' Notification emails will be sent to the client and attorney based on the referral email notification settings.' : ''#">
					<cfelse>
						<cfset local.qryGetReferralEmails = this.objAdminReferrals.getReferralEmails(referralID=this.referralID)>
						<cfset local.emailTypeList =  valueList(local.qryGetReferralEmails.emailTypeID)>
						<cfset doEmailReferral(event=arguments.event, callUID=local.callUID)>
						
						<cfif listLen(emailTypeList)>
							<cfset local.confMessage = "The data entered been saved, and the client information has been sent to the below Lawyer(s).">
						<cfelse>
							<cfset local.confMessage = "The information has been saved. The client will be referred to the below Lawyer(s). An e-mail will be sent to the client if a valid e-mail address was provided." />
						</cfif>
					</cfif>

				</cfif>
				<cfif not arguments.event.getValue("isFrontEnd",0) AND NOT val(arguments.event.getValue('payLater',0))>	
					<!--- If the client payment was skipped because of $0.00 sale, perform the following --->
					<cfset local.qryGetPaymentData = this.objAdminReferrals.getClientPaymentData(clientID=local.clientID)>
					<cfif not local.qryGetPaymentData.recordCount>
						<cfset arguments.event.setValue('clientReferralAmount','0.00') />
						<cfset local.saleStruct = structNew() />
						<cfset local.saleStruct = this.objAdminReferrals.recordClientSale(event=arguments.event) />
					</cfif>
				</cfif>

				<!--- reprocess conditions after all updations, members could be changed/added so not limiting to memberID --->
				<cfset this.objAdminReferrals.reprocessConditions(orgID=arguments.event.getValue('mc_siteInfo.orgID'), allMembers=1)>
			</cfif>
		</cfif>
		<cfif arguments.event.valueExists("isFrontEnd") and arguments.event.getValue("isFrontEnd",0)>
			<cfreturn local>
		<cfelseif local.referralTypeIsAgency>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					$(function() {
						alert("#local.confMessage#");
						window.location.href="#this.link.manageClientReferral#";
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		<cfelse>
			<cfset local.data = structNew()>
			<cfset local.data["confMessage"] = local.confMessage>
			<cfset local.data["selectedMemberIDs"] = ListToArray(arguments.event.getValue('selectedMemberIDs'))>
			<cfreturn returnAppStruct(serializeJson(local.data),"echo")>
		</cfif>

	</cffunction>	

	<cffunction name="managePanel" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.editPanelMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit') & "&tab=referral";
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit');
			local.urlString = "";
			local.panelList = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralPanelsList&mode=stream&referralID=#this.referralID#';
			local.panelMemberList = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralPanelMembersList&mode=stream&referralID=#this.referralID#&siteID=#arguments.event.getValue('mc_siteInfo.siteID')#&orgID=#arguments.event.getValue('mc_siteInfo.orgID')#';
			local.panelPageTitle = "Manage Panels";	
			
			arguments.event.paramValue('referralID',this.referralID);
			local.qryGetPanelStatuses = this.objAdminReferrals.getPanelStatuses(arguments.event);
			local.isPanelGroupDepend = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).isPanelGroupDepend;

			appendBreadCrumbs(arguments.event,{ link='', text='Manage Panels' });	
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>	
				<cfinclude template="dsp_panelManagement.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addPanel" access="public" output="false" returntype="struct" hint="Add Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.panelPageTitle = "Add Panel";		
			local.formLink = this.link.savePanel;
			local.urlString = "";
			local.objReferrals	= CreateObject("component","model.referrals.referrals");

			local.panelID = arguments.event.getValue('panelID',0);
			local.panelUID = createUUID();
			local.panelName = arguments.event.getValue('panelName','');
			local.panelShortDesc = arguments.event.getValue('panelShortDesc','');
			local.panelLongDesc = arguments.event.getValue('panelLongDesc','');
			local.panelInternalNotes = arguments.event.getValue('panelInternalNotes','');
			local.panelDateCreated = arguments.event.getValue('panelDateCreated','');
			local.panelDateCommitteeApproved = arguments.event.getValue('panelDateCommitteeApproved','');
			local.panelDateBoardApproved = arguments.event.getValue('panelDateBoardApproved','');
			local.panelDateBoardNotified = arguments.event.getValue('panelDateBoardNotified','');
			local.panelDateReviewed = arguments.event.getValue('panelDateReviewed','');
			local.panelSendMail = arguments.event.getValue('panelSendMail',1);
			local.panelMaxNumMembers = arguments.event.getValue('panelMaxNumMembers','');
			local.panelReferralFeePercent = arguments.event.getValue('panelReferralFeePercent','');
			local.panelDeductExpenseDesc = arguments.event.getValue('panelDeductExpenseDesc','');
			local.panelReferralAmount = arguments.event.getValue('panelReferralAmount','');
			local.panelParentID = arguments.event.getValue('panelParentID','');
			local.panelGLAccountID = arguments.event.getValue('panelGLAccountID','');
			local.panelGLAccountPath = arguments.event.getValue('panelGLAccountPath','');
			// Prepare GL Account widget data for Panel Revenue Override    Clear Selected GL Account
			local.strPanelGLAcctWidgetData = { label="Panel Revenue Override", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
				idFldName="panelGLAccountID", idFldValue=val(local.panelGLAccountID), pathFldValue=local.panelGLAccountPath, pathNoneTxt="(No override; uses referral's designated GL Account.)", clearBtnTxt="Clear Selected GL Account" };
			local.strPanelGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strPanelGLAcctWidgetData);

			local.panelStatusID = arguments.event.getValue('panelStatusID',1);
			local.panelClientFeeGLAccountID = arguments.event.getValue('panelClientFeeGLAccountID','');
			local.panelClientFeeGLAccountPath = arguments.event.getValue('panelClientFeeGLAccountPath','');	
			
			// Prepare GL Account widget data for Client Fees Revenue Override
			local.strClientFeeGLAcctWidgetData = { label="Client Fees Revenue Override", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
				idFldName="panelClientFeeGLAccountID", idFldValue=val(local.panelClientFeeGLAccountID), pathFldValue=local.panelClientFeeGLAccountPath, pathNoneTxt="(No override; uses referral's designated GL Account.)", clearBtnTxt="Clear Selected GL Account" };
			local.strClientFeeGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strClientFeeGLAcctWidgetData);

			local.panelClientReferralAmount = arguments.event.getValue('panelClientReferralAmount','');	
			local.panelSendReceiptEmail = arguments.event.getValue('panelSendReceiptEmail',1);
			local.allowPanelMgmt = arguments.event.getValue('allowPanelMgmt',1);
			local.feAllowClientReferral = arguments.event.getValue('feAllowClientReferral',1);
			local.feDspClientReferral = arguments.event.getValue('feDspClientReferral',1);
			local.ovFeeStructure = arguments.event.getValue('ovFeeStructure',0);
			local.feConfirmReferralContentID = arguments.event.getValue('feConfirmReferralContentID',0);
			local.feReviewSubmissionContentID = arguments.event.getValue('feReviewSubmissionContentID',0);
			local.feConfirmReferralContent = arguments.event.getValue('feConfirmReferralContent','Thank you for your submission!');
			local.feReviewSubmissionContent = arguments.event.getValue('feReviewSubmissionContent','Thank you for your submission!');
			
			local.statusName = "";
			local.panelStatusName = "";
			local.uploadDocs = "true";
			local.addSurveys = "true";
			local.siteResourceID = 0;
			local.qryGetPanelFeeStructureLevels = local.objReferrals.getPanelFeeStructureLevelsByID(panelID=local.panelID);
			local.referralFeeStructureID = local.objReferrals.getReferralFeeStructureID(referralID=this.referralID);
			local.totalFeeStructureTypeID = local.objReferrals.getFeeStructureTypes(typename="totals").feeStructureTypeID;
			local.isFeeStructureTotals = local.referralFeeStructureID EQ local.totalFeeStructureTypeID;
			
			// Build breadCrumb Trail ----------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link=this.link.managePanel, text=arguments.event.getValue('mc_adminNav.currentNavigationItem.navName') });
			appendBreadCrumbs(arguments.event,{ link='', text="Add Panel" });
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_panel.cfm" />			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="editPanel" access="public" output="false" returntype="struct" hint="Add Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.panelPageTitle = "Edit Panel";		
			local.formLink = this.link.savePanel;
			local.urlString = "";
			local.objReferrals	= CreateObject("component","model.referrals.referrals");

			local.panelID = arguments.event.getValue('panelID');
			
			local.qryGetPanelData = this.objAdminReferrals.getPanelByID(local.panelID);
			local.referralID = this.referralID;
			arguments.event.paramValue('referralID',local.referralID);
			local.qryGetPanelStatuses = this.objAdminReferrals.getPanelStatuses(arguments.event);
			
			local.panelUID = local.qryGetPanelData.uid;
			local.panelName = local.qryGetPanelData.name;
			local.panelShortDesc = local.qryGetPanelData.shortDesc;
			local.panelLongDesc = local.qryGetPanelData.longDesc;
			local.panelInternalNotes = local.qryGetPanelData.internalNotes;
			local.panelDateCreated = local.qryGetPanelData.dateCreated;
			local.panelDateCommitteeApproved = local.qryGetPanelData.dateCommitteeApproved;
			local.panelDateBoardApproved = local.qryGetPanelData.dateBoardApproved;
			local.panelDateBoardNotified = local.qryGetPanelData.dateBoardNotified;
			local.panelDateReviewed = local.qryGetPanelData.dateReviewed;
			local.panelSendMail = val(local.qryGetPanelData.sendMail);
			local.panelMaxNumMembers = local.qryGetPanelData.maxNumMembers;
			local.panelReferralFeePercent = local.qryGetPanelData.referralFeePercent;
			local.panelDeductExpenseDesc = local.qryGetPanelData.deductExpenseDesc;
			local.panelReferralAmount = local.qryGetPanelData.referralAmount;
			local.panelParentID = local.qryGetPanelData.panelParentID;
			local.panelStatusID = local.qryGetPanelData.statusID;
			local.statusName = local.qryGetPanelData.statusName;
			local.panelClientReferralAmount = local.qryGetPanelData.clientReferralAmount;
			local.panelSendReceiptEmail = val(local.qryGetPanelData.sendReceiptEmail);
			local.allowPanelMgmt = val(local.qryGetPanelData.allowPanelMgmt);
			local.feAllowClientReferral = val(local.qryGetPanelData.feAllowClientReferral);
			local.feDspClientReferral = val(local.qryGetPanelData.feDspClientReferral);
			local.ovFeeStructure = val(local.qryGetPanelData.ovFeeStructure);
			local.feConfirmReferralContentID = local.qryGetPanelData.feConfirmReferralContentID;
			local.feReviewSubmissionContentID = local.qryGetPanelData.feReviewSubmissionContentID;
			local.feConfirmReferralContent = local.qryGetPanelData.feConfirmReferralContent;
			local.feReviewSubmissionContent = local.qryGetPanelData.feReviewSubmissionContent;
			
			var panelStatusID = local.panelStatusID;
			local.qryCurrentPanelStatus = local.qryGetPanelStatuses.filter(function(row){
				return (arguments.row.panelStatusID eq panelStatusID);
			});
			local.panelStatusName = local.qryCurrentPanelStatus.statusName;

			local.uploadDocs = "false";
			local.addSurveys = "false";
			local.siteResourceID = local.qryGetPanelData.siteResourceID;

			local.panelGLAccountID = local.qryGetPanelData.GLAccountID;
			local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.panelGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			local.panelGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			
			local.panelClientFeeGLAccountID = local.qryGetPanelData.clientFeeGLAccountID;
			local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.panelClientFeeGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			local.panelClientFeeGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			
			// Prepare GL Account widget data for Panel Revenue Override    Clear Selected GL Account
			local.strPanelGLAcctWidgetData = { label="Panel Revenue Override", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
				idFldName="panelGLAccountID", idFldValue=val(local.panelGLAccountID), pathFldValue=local.panelGLAccountPath, pathNoneTxt="(No override; uses referral's designated GL Account.)", clearBtnTxt="Clear Selected GL Account" };
			local.strPanelGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strPanelGLAcctWidgetData);

			// Prepare GL Account widget data for Client Fees Revenue Override
			local.strClientFeeGLAcctWidgetData = { label="Client Fees Revenue Override", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
				idFldName="panelClientFeeGLAccountID", idFldValue=val(local.panelClientFeeGLAccountID), pathFldValue=local.panelClientFeeGLAccountPath, pathNoneTxt="(No override; uses referral's designated GL Account.)", clearBtnTxt="Clear Selected GL Account" };
			local.strClientFeeGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strClientFeeGLAcctWidgetData);

			local.qryGetPanelFeeStructureLevels = local.objReferrals.getPanelFeeStructureLevelsByID(panelID=local.panelID);
			local.referralFeeStructureID = local.objReferrals.getReferralFeeStructureID(referralID=this.referralID);
			local.totalFeeStructureTypeID = local.objReferrals.getFeeStructureTypes(typename="totals").feeStructureTypeID;
			local.isFeeStructureTotals = local.qryGetPanelFeeStructureLevels.feeStructureTypeID EQ local.totalFeeStructureTypeID;

			// Build breadCrumb Trail ----------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link=this.link.managePanel, text=arguments.event.getValue('mc_adminNav.currentNavigationItem.navName') });
			appendBreadCrumbs(arguments.event,{ link='', text="Edit Panel" });

			local.documentList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&mode=stream&panelID=#local.panelID#&meth=getPanelDocuments";	
			local.surveyList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&mode=stream&panelID=#local.panelID#&referralID=#this.referralID#&meth=getPanelSurveys";	
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_panel.cfm" />			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>	
	
	<cffunction name="savePanel" access="public" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.data = "";
			
			local.panelID = arguments.event.getValue('panelID');

			if (local.panelID)
				this.objAdminReferrals.updatePanel(arguments.event);
			else
				local.panelID = this.objAdminReferrals.insertPanel(arguments.event);
			
			application.objCommon.redirect('#this.link.managePanel#');			
		</cfscript>
	</cffunction>	
	
	<cffunction name="addSubPanel" access="public" output="false" returntype="struct" hint="Add Sub-Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.formLink = this.link.saveSubPanel;

			local.panelID = arguments.event.getValue('panelID',0);
			local.panelUID = createUUID();
			local.panelName = arguments.event.getValue('panelName','');
			local.panelShortDesc = arguments.event.getValue('panelShortDesc','');
			local.panelLongDesc = arguments.event.getValue('panelLongDesc','');
			local.panelParentID = arguments.event.getValue('panelParentID');
			local.panelStatusID = arguments.event.getValue('panelStatusID',1);
			local.statusName = "";
			local.panelStatusName = "";

			local.feDspClientReferral = arguments.event.getValue('feDspClientReferral',1);
			local.siteResourceID = 0;
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_subPanel.cfm" />			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>	
	
	<cffunction name="editSubPanel" access="public" output="false" returntype="struct" hint="Add Sub-Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();		
			local.formLink = this.link.saveSubPanel;
			local.panelID = arguments.event.getValue('panelID');
			
			local.qryGetPanelData = this.objAdminReferrals.getPanelByID(local.panelID);
			local.referralID = this.referralID;
			arguments.event.paramValue('referralID',local.referralID);			
			local.qryGetPanelStatuses = this.objAdminReferrals.getPanelStatuses(arguments.event);

			local.panelUID = local.qryGetPanelData.uid;			
			local.panelName = local.qryGetPanelData.name;
			local.panelShortDesc =  local.qryGetPanelData.shortDesc;
			local.panelLongDesc = local.qryGetPanelData.longDesc;
			local.panelParentID = local.qryGetPanelData.panelParentID;
			local.panelStatusID = local.qryGetPanelData.statusID;

			var panelStatusID = local.panelStatusID;
			local.qryCurrentPanelStatus = local.qryGetPanelStatuses.filter(function(row){
				return (arguments.row.panelStatusID eq panelStatusID);
			});

			local.panelStatusName = local.qryCurrentPanelStatus.statusName;
			local.statusName = local.qryGetPanelData.statusName;			
			local.feDspClientReferral = local.qryGetPanelData.feDspClientReferral;
			local.siteResourceID = local.qryGetPanelData.siteResourceID;			
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_subPanel.cfm" />			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>	

	<cffunction name="massEditPanels" access="public" output="false" returntype="struct" hint="mass edit panels">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();		
			local.formLink = buildCurrentLink(arguments.event,"updateAllSelectedPanels") & "&mode=direct";
			local.panelIDs = local.rc.strPanelsToUpdate;
			
			local.referralID = this.referralID;
			arguments.event.paramValue('referralID',local.referralID);			
			local.qryGetPanelStatuses = this.objAdminReferrals.getPanelStatuses(arguments.event);

		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_massEditPanel.cfm" />			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>		

	<cffunction name="updateAllSelectedPanels" access="public" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.data = "";			
			local.panelIDs = arguments.event.getValue('panelIDs');	
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif len(local.panelIDs)>
					
					<cfset local.updateObj = this.objAdminReferrals.updatePanelsByIds(arguments.event)>
					<cfif local.updateObj.success>
						<div class="mb-1 alert alert-success alert-dismissible" role="alert">
							Panels Updated Successfully.
						</div>
						<script language="javascript">
							top.panelListTable.draw();
							top.$('##MCModalFooter ##btnMCModalSave').hide()
							setTimeout(() => {
								top.MCModalUtils.hideModal();
							}, 5000);
						</script>
					<cfelse>
						<div class="mb-1 alert alert-danger alert-dismissible" role="alert">
							Unable to update the panels.
						</div>
						<script language="javascript">
							top.$('##MCModalFooter ##btnMCModalSave').hide()
							setTimeout(() => {
								top.MCModalUtils.hideModal();
							}, 5000);
						</script>
					</cfif>
				<cfelse>
					<div class="mb-1 alert alert-danger alert-dismissible" role="alert">
                        Unable to update the panels.
                    </div>
					<script language="javascript">
						top.$('##MCModalFooter ##btnMCModalSave').hide()
						setTimeout(() => {
							top.MCModalUtils.hideModal();
						}, 5000);
					</script>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo") />			
	</cffunction>	
	<cffunction name="saveSubPanel" access="public" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.data = "";
			
			local.panelID = arguments.event.getValue('panelID');
	
			if (local.panelID)
				this.objAdminReferrals.updatePanel(arguments.event);
			else
				local.panelID = this.objAdminReferrals.insertPanel(arguments.event);	
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadPanelList();
				top.refreshGrids();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo") />			
	</cffunction>	
	
	<cffunction name="addDocument" access="public" output="false" returntype="struct" hint="edit document">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew() />
		
		<!--- Load Objects --->
		<cfset local.objDocument = CreateObject("component","model.admin.documents.documentAdmin") />
		<cfset local.objSection = CreateObject("component","model.system.platform.section") />
		
		<!--- Get Data --->
		<cfset local.qryGetPanelDocument = this.objAdminReferrals.getPanelDocumentByID(panelDocumentID=arguments.event.getValue('_pdid',0)) />
		<cfset local.docStatus	= local.objDocument.getDocumentStatuses() />
		<cfset local.formAction	= this.link.saveDocument />
		<cfset local.rootSectionID	= local.objSection.getRootSectionID(arguments.event.getValue('mc_siteinfo.siteid')) />
		
		<cfscript>
		// set document information ----------------------------------------------------------------- ::
			arguments.event.setValue('siteResourceID',local.qryGetPanelDocument.siteResourceID);
			arguments.event.setValue('documentID',local.qryGetPanelDocument.documentID);
			arguments.event.setValue('docSectionID',local.rootSectionID);
			arguments.event.setValue('documentLanguageID',local.qryGetPanelDocument.documentLanguageID);
			arguments.event.setValue('documentVersionID',local.qryGetPanelDocument.documentVersionID);
			arguments.event.setValue('languageID',local.qryGetPanelDocument.languageID);
			arguments.event.setValue('docTitle',local.qryGetPanelDocument.docTitle);
			arguments.event.setValue('docDesc',local.qryGetPanelDocument.docDesc);
			arguments.event.setValue('fileName',local.qryGetPanelDocument.fileName);
			arguments.event.setValue('fileExt',local.qryGetPanelDocument.fileExt);
			arguments.event.setValue('dateCreated',local.qryGetPanelDocument.dateCreated);
			arguments.event.setValue('dateModified',local.qryGetPanelDocument.dateModified);
			arguments.event.setValue('documentStatusID',local.qryGetPanelDocument.siteResourceStatusID);
			arguments.event.setValue('author','');
			arguments.event.setValue('panelID',arguments.event.getValue('panelID',0));
			arguments.event.setValue('clientReferralID',arguments.event.getValue('clientReferralID',0));
			arguments.event.paramValue('newFile','');
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_document.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>	
	
	<cffunction name="editDocument" access="public" output="false" returntype="struct" hint="edit document">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew() />
		<cfset local.siteID = arguments.event.getValue('mc_siteinfo.siteid')>
		<!--- Load Objects --->
		<cfset local.objDocumentAdmin = CreateObject("component","model.admin.documents.documentAdmin") />
		<cfset local.objDocument = CreateObject("component","model.system.platform.document") />
		<cfset local.objSection = CreateObject("component","model.system.platform.section") />
		
		<!--- Get Data --->
		<cfset local.qryGetDocument = local.objDocument.getAdminDocumentData(arguments.event.getValue('documentID'),arguments.event.getValue('documentVersionID'),arguments.event.getValue('activeOnly',1),session.mcStruct.languageCode,arguments.event.getValue('activeSiteResource',0)) />
		<cfset local.documentVersions = this.objAdminReferrals.getDocumentVersions(arguments.event.getValue('documentID')) />
		<cfset local.docStatus	= local.objDocumentAdmin.getDocumentStatuses() />
		<cfset local.formAction	= this.link.saveDocument />
		<cfset local.rootSectionID	= local.objSection.getRootSectionID(local.siteID) />
		<cfset local.getSections = local.objSection.getRecursiveSections(siteID=local.siteID, startSectionID=local.rootSectionID) />		
		
		<cfscript>
		// set document information ----------------------------------------------------------------- ::
			arguments.event.setValue('siteResourceID',local.qryGetDocument.siteResourceID);
			arguments.event.setValue('documentID',local.qryGetDocument.documentID);
			arguments.event.setValue('docSectionID',local.qryGetDocument.sectionID);
			arguments.event.setValue('documentLanguageID',local.qryGetDocument.documentLanguageID);
			arguments.event.setValue('documentVersionID',local.qryGetDocument.documentVersionID);
			arguments.event.setValue('languageID',local.qryGetDocument.languageID);
			arguments.event.setValue('docTitle',local.qryGetDocument.docTitle);
			arguments.event.setValue('docDesc',local.qryGetDocument.docDesc);
			arguments.event.setValue('fileName',local.qryGetDocument.fileName);
			arguments.event.setValue('fileExt',local.qryGetDocument.fileExt);
			arguments.event.setValue('dateCreated',local.qryGetDocument.dateCreated);
			arguments.event.setValue('dateModified',local.qryGetDocument.dateModified);
			arguments.event.setValue('documentStatusID',local.qryGetDocument.siteResourceStatusID);
			arguments.event.setValue('author',local.qryGetDocument.author);
			arguments.event.setValue('panelID',arguments.event.getValue('panelID',0));
			arguments.event.setValue('clientReferralID',arguments.event.getValue('clientReferralID',0));
			arguments.event.paramValue('newFile','');
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_document.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>		
	
	<cffunction name="saveDocument" access="public" output="false" returntype="struct" hint="saves a document">
		<cfargument name="event" type="any" />
		
		<cfset var local = structNew() />
		<cfset local.data = "" />
		
		<!--- :: Load Objects  :: --->
		<cfset local.objDocument = CreateObject("component","model.system.platform.document") />
		<cfset local.objDocumentAdmin = CreateObject("component","model.admin.documents.documentAdmin") />

		<cfif len(arguments.event.getValue('documentID'))>
			<!--- if there's a documentID then update the document --->
			<cfscript>
				//fetch current document data
				if(val(arguments.event.getValue('panelID',0))) {
					local.qryGetCurrentDocDetails = this.objAdminReferrals.getPanelDocumentInfo(panelID=arguments.event.getValue('panelID'), documentID=arguments.event.getValue('documentID'));
				} else if(val(arguments.event.getValue('clientReferralID',0))){
					local.qryGetCurrentDocDetails = this.objAdminReferrals.getReferralCaseDocumentByID(arguments.event.getValue('documentID'));
				}
				//if( NOT local.security.edit ){ application.objCommon.redirect('#this.link.message#&message=1'); }
				// UPLOAD PROCEDURE ------------------------------------------------------------------------- ::
				local.DocID = arguments.event.getValue("documentID");
				local.documentVersionID = arguments.event.getValue("documentVersionID");
				local.newContributorMemberID = 0;
				// check to see if there is a new file to upload -------------------------------------------- ::
				if( arguments.event.getTrimValue('newFile') NEQ "" ){
					// if yes then set fileToUpload to the form variable newFile ------------------------------ ::
					arguments.event.setValue('fileToUpload','newFile');
					// pre set the fileUploaded variable to TRUE ---------------------------------------------- ::
					local.fileUploaded = TRUE;
					// try to upload the file to the proper destination --------------------------------------- ::
					try { local.newFile = local.objDocument.uploadFile("form.newFile"); } 
					// if if fails to upload the set the fileUploaded flag to FALSE --------------------------- ::
					catch(any excpt) { local.fileUploaded = FALSE; }
					// if file was uploaded then get the files new source data -------------------------------- ::
					if( local.fileUploaded ){
						local.objDocument.forceFileExtentionIfBlank(local.newFile);
						arguments.event.setValue('fileName',local.newFile.clientFile);
						arguments.event.setValue('fileExt',local.newFile.clientFileExt);

						local.newContributorMemberID = session.cfcuser.memberdata.memberID;
						// if a new file is being uploaded, then create a new version ------------------------------------------------------- ::
						local.documentVersionID = local.objDocument.insertVersion(
									orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), 
									siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), 
									fileData=local.newFile, 
									documentLanguageID=arguments.event.getValue('documentLanguageID'),
									author=arguments.event.getTrimValue('author'),
									contributorMemberID=local.newContributorMemberID,
									recordedByMemberID=local.newContributorMemberID,
									oldFileExt=arguments.event.getValue('fileExt'));
					}
					// error in upload - locate to message page and apply message --------------------------- ::
					else{ application.objCommon.redirect('#this.link.message#&message=3'); }
				}
				// if there's a new file uploaded, create new version, otherwise update current version	//
				// Update Procedure ------------------------------------------------------------------------- ::
				local.fileData = { clientFile=arguments.event.getValue('fileName'), serverFileExt=arguments.event.getValue('fileExt') };
				local.loadDocument = local.objDocument.updateDocument(
												documentID=arguments.event.getTrimValue('documentID'),
												siteID=arguments.event.getTrimValue('mc_siteInfo.siteID'),
												docTitle=arguments.event.getTrimValue('docTitle'),
												docDesc=arguments.event.getTrimValue('docDesc'),
												fileData=local.fileData,
												sectionID=arguments.event.getTrimValue('docSectionID'),
												author=arguments.event.getTrimValue('author',''),
												contributorMemberID=session.cfcuser.memberdata.memberID,
												recordedByMemberID=session.cfcuser.memberdata.memberID,
												newFileUploaded=0);
				if(val(arguments.event.getValue('panelID',0))){								
					this.objAdminReferrals.addPanelDocUpdateHistory(orgID=arguments.event.getTrimValue('mc_siteInfo.orgID'), siteID=arguments.event.getTrimValue('mc_siteInfo.siteID'), 
						panelID=arguments.event.getValue('panelID'), documentID=arguments.event.getValue('documentID'), docTitle=local.qryGetCurrentDocDetails.docTitle,
						docDesc=local.qryGetCurrentDocDetails.docDesc, fileName=local.qryGetCurrentDocDetails.fileName);
				} else if(val(arguments.event.getValue('clientReferralID',0))){
					local.changes = ArrayNew(1);
					if(local.qryGetCurrentDocDetails.docTitle NEQ arguments.event.getTrimValue('docTitle')) {
						local.thisChangeStr =  {ITEM="Title",OLDVALUE=local.qryGetCurrentDocDetails.docTitle,NEWVALUE=arguments.event.getTrimValue('docTitle')};
						arrayAppend(local.changes, local.thisChangeStr);
					}
					if(local.qryGetCurrentDocDetails.docDesc NEQ arguments.event.getTrimValue('docDesc')) {
						local.thisChangeStr =  {ITEM="Description",OLDVALUE=local.qryGetCurrentDocDetails.docDesc,NEWVALUE=arguments.event.getTrimValue('docDesc')};
						arrayAppend(local.changes, local.thisChangeStr);
					}
					if(local.qryGetCurrentDocDetails.fileName NEQ arguments.event.getTrimValue('fileName')) {
						local.thisChangeStr =  {ITEM="File",OLDVALUE=local.qryGetCurrentDocDetails.fileName,NEWVALUE=arguments.event.getValue('fileName')};
						arrayAppend(local.changes, local.thisChangeStr);
					}

					createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')), clientReferralID=int(arguments.event.getValue('clientReferralID',0)), actorMemberID=int(session.cfcuser.memberdata.memberid), 				mainMessage="Document Updated (Admin) - #arguments.event.getTrimValue('docTitle')#", changes=local.changes );
				}
			</cfscript>
		<cfelse>

			<!--- if there's no documentID than insert a new document --->
			<cfscript>
				arguments.event.setValue('fileToUpload','newFile');
				local.fileUploaded = TRUE;
				try {
					local.newFile = local.objDocument.uploadFile("form.newFile");
					local.fileUploaded = local.newFile.uploadComplete;
				} 
				catch(any excpt) { local.fileUploaded = FALSE; }
				if( local.fileUploaded ){
					// get new fileExt ------------------------------------------------------------------------ ::
					local.objDocument.forceFileExtentionIfBlank(local.newFile);
					arguments.event.setValue('fileName',local.newFile.clientFile);
					arguments.event.setValue('fileExt',local.newFile.serverFileExt);
					// insert new docData into database ------------------------------------------------------- ::
					local.insertResults = local.objDocument.insertDocument(
										siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										resourceType='ApplicationCreatedDocument',
										parentSiteResourceID=this.siteResourceID,
										sectionID=arguments.event.getValue('docSectionID'),
										docTitle=arguments.event.getTrimValue('docTitle'),
										docDesc=arguments.event.getTrimValue('docDesc'),
										author=arguments.event.getTrimValue('author'),
										fileData=local.newFile,
										isActive=1,
										isVisible=true,
										contributorMemberID=session.cfcuser.memberdata.memberid,
										recordedByMemberID=session.cfcuser.memberdata.memberid,
										oldFileExt=arguments.event.getValue('fileExt'));
					if(val(arguments.event.getValue('panelID',0))){
						this.objAdminReferrals.savePanelDocument(orgID=arguments.event.getTrimValue('mc_siteInfo.orgID'), siteID=arguments.event.getTrimValue('mc_siteInfo.siteID'), 
							panelID=arguments.event.getValue('panelID'), documentID=local.insertResults.documentID);
					} else if(val(arguments.event.getValue('clientReferralID',0))){
						this.objAdminReferrals.saveCaseDocument(arguments.event.getValue('clientReferralID'), local.insertResults.documentID);

						createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')), clientReferralID=int(arguments.event.getValue('clientReferralID',0)), actorMemberID=int(session.cfcuser.memberdata.memberid), 				mainMessage="Document Uploaded (Admin) - #arguments.event.getTrimValue('docTitle')#");
					}
				}
			</cfscript>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshGrids();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="manageMainSettings" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.objEmailTemplateAdmin = createObject("component","model.admin.emailTemplates.emailTemplateAdmin");
			local.objEmailTemplate = createObject("component","model.admin.emailTemplates.emailTemplates");
			local.objReferrals	= CreateObject("component","model.referrals.referrals");

			local.data = "";
			local.siteID = arguments.event.getValue('mc_siteinfo.siteid');
			local.link.referralSettings = "#arguments.event.getValue('mc_adminNav.adminHome')#&mca_s=#arguments.event.getValue('mca_s','')#&mca_a=#arguments.event.getValue('mca_lt','')#&mca_tt=&mca_ta=&mca_hk=1";

			// GET DATA --------------------------------------------------------------------------------- ::
			local.qryControlPanelPendingStatuses = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID, isPending=1, canEditLawyer=1, canRefer=1).qryStatus;
			local.qryPendingCoordinationStatus = new Query(
				sql="SELECT * FROM qryStatuses WHERE isCoordination = 1",
				dbtype="query",
				qryStatuses=local.qryControlPanelPendingStatuses
			).execute().getResult();

			local.objFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector");
			local.arrReferralET = arrayNew(1);
			local.arrReferralSMSET = arrayNew(1);

			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFCLIENTS',
				title="New Referral to Client", 
				intro="Here you manage Referral To Client Templates.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			arrayAppend(local.arrReferralET,local.strETData);

			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFMEMBERS',
				title="New Referral to Members", 
				intro="Here you manage Referral to Members Templates.",
				gridext="#this.siteResourceID#_2",
				gridwidth=690,
				initGridOnLoad=false
			};
			arrayAppend(local.arrReferralET,local.strETData);

			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFCLIENTRCPT',
				title="Client Receipt", 
				intro="Here you manage Client Receipt Templates.",
				gridext="#this.siteResourceID#_3",
				gridwidth=690,
				initGridOnLoad=false
			};
			arrayAppend(local.arrReferralET,local.strETData);

			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFFEEDSCRPNCY',
				title="Fee Discrepancy Templates", 
				intro="Here you manage Fee Submission Link Templates.",
				gridext="#this.siteResourceID#_4",
				gridwidth=690,
				initGridOnLoad=false
			};
			arrayAppend(local.arrReferralET,local.strETData);

			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFREPORT',
				title="Automated Referral Reports", 
				intro="Here you manage Automated Referral Report Templates.",
				gridext="#this.siteResourceID#_5",
				gridwidth=690,
				initGridOnLoad=false
			};
			arrayAppend(local.arrReferralET,local.strETData);
			
			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFCSTRACKREPORT',
				title="Automated Internal Case Tracking Reports", 
				intro="Here you manage Automated Internal Case Tracking Report Templates.",
				gridext="#this.siteResourceID#_6",
				gridwidth=690,
				initGridOnLoad=false
			};
			arrayAppend(local.arrReferralET,local.strETData);

			if (local.qryPendingCoordinationStatus.recordCount) {
				local.strETData = { 
					siteID=local.siteID,
					treeCode='ETREFCLIENTPENDCOORD',
					title="New Referral to Client in Pending - Coordination", 
					intro="Here you manage Referral to Clients in Pending - Coordination Templates.",
					gridext="#this.siteResourceID#_7",
					gridwidth=690,
					initGridOnLoad=false
				};
				arrayAppend(local.arrReferralET,local.strETData);

				local.strETData = { 
					siteID=local.siteID,
					treeCode='ETREFMEMBERPENDCOORD',
					title="New Referral to Member in Pending - Coordination", 
					intro="Here you manage Referral to Members in Pending - Coordination Templates.",
					gridext="#this.siteResourceID#_8",
					gridwidth=690,
					initGridOnLoad=false
				};
				arrayAppend(local.arrReferralET,local.strETData);
			}

			local.arrEmailTemplatesGrid = arrayNew(1);
			for (local.strETData in local.arrReferralET) {
				arrayAppend(local.arrEmailTemplatesGrid,local.objEmailTemplateAdmin.manageEmailTemplates(strETData=local.strETData));
			}
			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Main Settings' });
		</cfscript>
		
		<cfif arguments.event.valueExists('saveFrm')>
			<cfset this.objAdminReferrals.saveSettings(arguments.event) />
		</cfif>

		<cfset local.qrySearchFieldSet = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='referralsearch') />
		<cfset local.strSearchFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="searchFieldsetID", selectedValue=val(local.qrySearchFieldSet.fieldsetID))>

		<cfset local.searchUseID = 0 />
		<cfif len(local.qrySearchFieldSet.useID)>
			<cfset local.searchUseID = local.qrySearchFieldSet.useID />
		</cfif>

		<cfset local.qryResultsFieldSet = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='referralresult') />
		<cfset local.strResultsFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="resultsFieldsetID", selectedValue=val(local.qryResultsFieldSet.fieldsetID))>

		<cfset local.resultsUseID = 0 />
		<cfif len(local.qryResultsFieldSet.useID)>
			<cfset local.resultsUseID = local.qryResultsFieldSet.useID />
		</cfif>
		
		<cfset local.qryClientFeeResultsFieldSet = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='clientfeeresult') />
		<cfset local.strClientFeeFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="clientFeeResultsFieldsetID", selectedValue=val(local.qryClientFeeResultsFieldSet.fieldsetID))>

		<cfset local.clientFeeResultsUseID = 0 />
		<cfif len(local.qryClientFeeResultsFieldSet.useID)>
			<cfset local.clientFeeResultsUseID = local.qryClientFeeResultsFieldSet.useID />
		</cfif>		

		<cfset local.qryClientEmailConfirmationFieldSet = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='clientemail') />
		<cfset local.strEmailConfirmationFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="clientEmailConfirmationFieldsetID", selectedValue=val(local.qryClientEmailConfirmationFieldSet.fieldsetID))>

		<cfset local.clientEmailConfirmationUseID = 0 />
		<cfif len(local.qryClientEmailConfirmationFieldSet.useID)>
			<cfset local.clientEmailConfirmationUseID = local.qryClientEmailConfirmationFieldSet.useID />
		</cfif>	
	
		<cfset local.qryChecklistFieldSet = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='panelmemberchecklist') />
		<cfset local.strCheckListFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="checklistFieldsetID", selectedValue=val(local.qryChecklistFieldSet.fieldsetID))>
	
		<cfset local.checklistUseID = 0 />
		<cfif len(local.qryChecklistFieldSet.useID)>
			<cfset local.checklistUseID = local.qryChecklistFieldSet.useID />
		</cfif>	
		<cfset arguments.event.setValue('referralID',int(this.referralID))>

		<cfsavecontent variable="local.referralCenterInstruction">
			<cfoutput>
				To receive information about your referrals via text as well as through email, please verify your mobile or texting eligible number below. 

				To stop receiving text messages for your referrals.
			</cfoutput>
		</cfsavecontent>
		<cfquery name="local.qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @appCreatedContentResourceTypeID INT, @siteID INT,
			@defaultLanguageID INT,@feReferralCenterInstructionForMemberID INT, @contentSiteResourceID INT,
			@referralCenterInstruction VARCHAR(MAX), @referralID INT;
			
			SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
			SELECT @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">;
			SELECT @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.referralID#">;
			SELECT @defaultLanguageID = defaultLanguageID FROM dbo.sites WHERE siteID = @siteID;
			SELECT @referralCenterInstruction = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.referralCenterInstruction#">;

			IF EXISTS(SELECT * FROM dbo.ref_referrals WHERE referralID =  @referralID AND  feReferralCenterInstructionForMemberID IS NULL)
			BEGIN
				EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID,
				@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, @isActive=1,
				@contentTitle=null, @contentDesc=null, @rawContent=@referralCenterInstruction, @contentID=@feReferralCenterInstructionForMemberID OUTPUT,
				@siteResourceID=@contentSiteResourceID OUTPUT;

				UPDATE dbo.ref_referrals
					SET feReferralCenterInstructionForMemberID = @feReferralCenterInstructionForMemberID	
					WHERE referralID = @referralID; 
			END	
		</cfquery>	
		
		<cfset local.qryGetReferralTypes = this.objAdminReferrals.getReferralTypes(event=arguments.event)>
		<cfset local.qryStates = application.objCommon.getStates() />
		<cfset local.qryReferralSettings = this.objAdminReferrals.getReferralSettings(local.siteID) />
		<cfset local.title = local.qryReferralSettings.title />	
		<cfset local.email = local.qryReferralSettings.emailRecipient />
		<cfset local.defaultStateID = local.qryReferralSettings.defaultStateID />
		<cfset local.defaultCity = local.qryReferralSettings.defaultCity />
		<cfset local.defaultCallType = local.qryReferralSettings.defaultCallTypeId />
		<cfset local.defaultClientSurvey = local.qryReferralSettings.defaultClientSurvey>
		<cfset local.referralGLAccountID = local.qryReferralSettings.GLAccountID />	
		<cfset local.referralClientGLAccountID = local.qryReferralSettings.clientGLAccountID />
		<cfset local.clientFeeMemberID = local.qryReferralSettings.clientFeeMemberID />	
		<cfset local.clientFeeMemberName = local.qryReferralSettings.clientFeeMemberName />
		<cfset local.rotationByPanel = local.qryReferralSettings.rotationByPanel />
		<cfset local.isPanelGroupDepend = local.qryReferralSettings.isPanelGroupDepend />
		<cfset local.clientEmailTemplateId = local.qryReferralSettings.clientEmailTemplateId />
		<cfset local.memberEmailTemplateId = local.qryReferralSettings.memberEmailTemplateId />
		<cfset local.receiptEmailTemplateId = local.qryReferralSettings.receiptEmailTemplateId />
		<cfset local.clientMailTopTxt = local.qryReferralSettings.clientMailTopTxt />
		<cfset local.clientMailBottomTxt = local.qryReferralSettings.clientMailBottomTxt />
		<cfset local.memberMailTopTxt = local.qryReferralSettings.memberMailTopTxt />
		<cfset local.memberMailBottomTxt = local.qryReferralSettings.memberMailBottomTxt />
		<cfset local.systemMailCaseActivityTxt = local.qryReferralSettings.systemMailCaseActivityTxt />	
		<cfset local.dspImportClientReferralID = local.qryReferralSettings.dspImportClientReferralID />	
		<cfset local.dspLessFilingFeeCosts = local.qryReferralSettings.dspLessFilingFeeCosts />
		<cfset local.allowFeeTypeMgmt = local.qryReferralSettings.allowFeeTypeMgmt />
		<cfset local.deductFilingFee = local.qryReferralSettings.deductFilingFee />
		<cfset local.dspLegalDescription = val(local.qryReferralSettings.dspLegalDescription) />
		<cfset local.collectClientFeeFE = val(local.qryReferralSettings.collectClientFeeFE) />
		<cfset local.allowFeeDiscrepancy = val(local.qryReferralSettings.allowFeeDiscrepancy) />
		<cfset local.feeDiscrepancyAmt = local.qryReferralSettings.feeDiscrepancyAmt />
		<cfset local.collectClientFeeFEOverrideTxt = local.qryReferralSettings.collectClientFeeFEOverrideTxt />
		<cfset local.qryGetPanelFeeStructureLevels = local.objReferrals.getPanelFeeStructureLevelsByID(referralID=this.referralID) />
		<cfset local.emailAgencyInfoToClient = val(local.qryReferralSettings.emailAgencyInfoToClient) />
		<cfset local.emailAgencyInfoToClientTopTxt = local.qryReferralSettings.emailAgencyInfoToClientTopTxt />
		<cfset local.emailAgencyInfoToClientBottomTxt = local.qryReferralSettings.emailAgencyInfoToClientBottomTxt />
		<cfset local.memEmailOptOutGroupID = local.qryReferralSettings.memEmailOptOutGroupID />
		<cfset local.memEmailOptOutGroupPath = local.qryReferralSettings.memEmailOptOutGroupPath />
		<cfset local.counselorGroupID = local.qryReferralSettings.counselorGroupID />
		<cfset local.counselorAssignmentGroupPath = local.qryReferralSettings.counselorAssignmentGroupPath />
		<cfset local.feReferralCenterInstructionForMemberID = local.qryReferralSettings.feReferralCenterInstructionForMemberID />
		<cfset local.feReferralCenterInstructionForMemberContent = local.qryReferralSettings.feReferralCenterInstructionForMemberContent />
		<cfset local.feeDiscrepancyReferralStatusIDs = ''>			

		<cfscript>
			local.objSMSTemplate = createObject("component","model.admin.common.modules.smsTemplate.smsTemplate");

			local.referralStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID, isActive=1);
			if(local.referralStatusResponseStruct.success){
				local.qryGetClientReferralStatuses = local.referralStatusResponseStruct.qryStatus;
			} else {
				return showRefError('There are no active Referral Statuses. Please contact administrator.');
			}			

			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFCLIENTS',
				section='client',
				title="Client Text Messaging", 
				intro="Here you manage Referral To Client Templates.",
				gridext="#this.siteResourceID#text_1",
				gridwidth=690,
				initGridOnLoad=true,
				referralID=this.referralID,
				SMSApplicationType='referrals',
				messagingServiceRefUsage= local.objSMSTemplate.getMessagingServiceUsageByTypeCode(local.siteID,'REFCLIENTS'),
				qryTemplateDetails= local.objSMSTemplate.getTemplateTriggersList(local.siteID,'REFCLIENTS')
			};
			arrayAppend(local.arrReferralSMSET,local.strETData);

			local.strETData = { 
				siteID=local.siteID,
				treeCode='ETREFMEMBERS',
				section='member',
				feReferralCenterInstructionForMemberID=local.feReferralCenterInstructionForMemberID, 
				feReferralCenterInstructionForMemberContent=local.feReferralCenterInstructionForMemberContent, 
				title="Member Text Messaging", 
				intro="Here you manage Referral To Members Templates.",
				gridext="#this.siteResourceID#text_2",
				gridwidth=690,
				initGridOnLoad=true,
				referralID=this.referralID,
				SMSApplicationType='referrals',
				messagingServiceRefUsage= local.objSMSTemplate.getMessagingServiceUsageByTypeCode(local.siteID,'REFMEMBERS'),
				qryTemplateDetails = local.objSMSTemplate.getTemplateTriggersList(local.siteID,'REFMEMBERS')
			};
			arrayAppend(local.arrReferralSMSET,local.strETData);

			local.arrSMSTemplatesGrid = arrayNew(1);
			
			for (local.strETData in local.arrReferralSMSET) {				
				arrayAppend(local.arrSMSTemplatesGrid,local.objSMSTemplate.manageSMSTemplates(strETData=local.strETData));
			}
		</cfscript>
		<cfif local.allowFeeDiscrepancy>
			<cfset local.feeDiscrepancyReferralStatusIDs = local.qryGetClientReferralStatuses>
			<cfset local.qryFeeDiscrepancyReferralStatusIDs = local.qryGetClientReferralStatuses.filter(function(row){
				return (arguments.row.isFeeDiscrepancyAssess eq 1);
			})>
			<cfset local.feeDiscrepancyReferralStatusIDs = valueList(local.qryFeeDiscrepancyReferralStatusIDs.clientReferralStatusID)>
		</cfif>
		<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.referralGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
		<cfset local.referralGLAccountPath = len(local.tmpStrAccount.qryAccount.thePathExpanded) ? local.tmpStrAccount.qryAccount.thePathExpanded : "" />
		<cfset local.strReferralGLAcctWidgetData = { label="Default Referral Revenue Account", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
			idFldName="referralGLAccountID", idFldValue=val(local.referralGLAccountID), pathFldValue=local.referralGLAccountPath, pathNoneTxt="(no account selected)"}>
		<cfset local.strReferralGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strReferralGLAcctWidgetData)>


		<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.referralClientGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
		<cfset local.referralClientGLAccountPath = len(local.tmpStrAccount.qryAccount.thePathExpanded) ? local.tmpStrAccount.qryAccount.thePathExpanded : "" />
		<cfset local.strClientGLAcctWidgetData = { label="Default Admin Client Fees Revenue Account", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
			idFldName="referralClientGLAccountID", idFldValue=val(local.referralClientGLAccountID), pathFldValue=local.referralClientGLAccountPath, pathNoneTxt="(no account selected)"}>
		<cfset local.strClientGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strClientGLAcctWidgetData)>

		<cfset local.qryGetAllPaymentProfiles = this.objAdminReferrals.getAllPaymentProfiles(local.siteID) />	
		<cfset local.qryGetReferralPaymentPrifiles = this.objAdminReferrals.getReferralPaymentProfiles(this.applicationInstanceID) />
		<cfset local.payProfileList = valueList(local.qryGetReferralPaymentPrifiles.profileID) />
		
		<cfset local.feeTypeLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getFeeTypes&mode=stream&referralID=#this.referralID#">
		<cfset local.panelLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getPanelList&mode=stream&referralID=#this.referralID#">
		
		<cfset local.qryGetOrgEmails = this.objAdminReferrals.getOrgEmails(orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
		
		<cfset local.qryGetClientEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFCLIENTS')>
		<cfset local.qryGetMemberEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFMEMBERS')>
		<cfset local.qryGetClientReceipttEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFCLIENTRCPT')>
		<cfset local.qryGetFeeSubmissionEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFFEEDSCRPNCY')>
		<cfset local.qryPendingCoordClientEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFCLIENTPENDCOORD')>
		<cfset local.qryPendingCoordMemberEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFMEMBERPENDCOORD')>
		
		<cfset local.qryGetReferralEmails = this.objAdminReferrals.getReferralEmails(referralID=this.referralID) />
		<cfset local.emailTypeList =  valueList(local.qryGetReferralEmails.emailTypeID) />
		
		<cfset local.qryPanelStatuses = this.objAdminReferrals.getPanelMemberStatuses(referralID=this.referralID) />
		<cfset local.qryPanelAppStatuses = this.objAdminReferrals.getPanelMemberAppStatuses(referralID=this.referralID) />
		
		<cfset local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') />
		<cfset local.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups')/>
		<!--- Front-End Client Referral Settings --->
		<cfset local.qryClientReferralSettings = CreateObject("component","model.clientReferrals.clientReferrals").getClientReferralSettings(siteID=local.qryReferralSettings.siteID) />
		<cfset local.feSiteResourceID = local.qryClientReferralSettings.siteResourceID>
		<cfset local.qryGetIntakeFormFieldDetails = this.objAdminReferrals.getIntakeFormFieldDetails( referralID=this.referralID) />

		<cfset local.isFeeStructureTotals = 0>
		<cfset local.feeStructureTypeID = val(local.qryClientReferralSettings.feeStructureTypeID) />	
		<cfset local.totalFeeStructureTypeID = local.objReferrals.getFeeStructureTypes(typename="totals").feeStructureTypeID>
		<cfif local.totalFeeStructureTypeID EQ local.feeStructureTypeID ><cfset local.isFeeStructureTotals = 1></cfif>

		<cfif val(local.feSiteResourceID)>
			<cfset local.qryGetIntakeFormFieldsToTrack = this.objAdminReferrals.getIntakeFormFieldsToTrack(referralID=this.referralID) />

			<cfset local.qryClientSearchFieldSet = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.feSiteResourceID, area='clientreferralsearch') />			
			<cfset local.strClientSearchFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="clientSearchFieldsetID", selectedValue=val(local.qryClientSearchFieldSet.fieldsetID))>

			<cfset local.clientSearchUseID = 0 />
			<cfif len(local.qryClientSearchFieldSet.useID)>
				<cfset local.clientSearchUseID = local.qryClientSearchFieldSet.useID />
			</cfif>
	
			<cfset local.qryClientResultsFieldSet = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.feSiteResourceID, area='clientreferralresult') />
			<cfset local.strClientResultsFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="clientResultsFieldsetID", selectedValue=val(local.qryClientResultsFieldSet.fieldsetID))>	
			
			<cfset local.clientResultsUseID = 0 />
			<cfif len(local.qryClientResultsFieldSet.useID)>
				<cfset local.clientResultsUseID = local.qryClientResultsFieldSet.useID />
			</cfif>
	
			<cfset local.fePanelInfoContentID = val(local.qryClientReferralSettings.fePanelInfoContentID) />
			<cfset local.fePanelInfoContent = local.qryClientReferralSettings.fePanelInfoContent />			
			<cfset local.feNoResultsInfoContentID = val(local.qryClientReferralSettings.feNoResultsInfoContentID) />
			<cfset local.feNoResultsInfoContent = local.qryClientReferralSettings.feNoResultsInfoContent />		
			<cfset local.feLegalDescInstructContentID = val(local.qryClientReferralSettings.feLegalDescInstructContentID) />
			<cfset local.feLegalDescInstructContent = local.qryClientReferralSettings.feLegalDescInstructContent />						
			<cfset local.feCounselorID = local.qryClientReferralSettings.feCounselorID />
			<cfset local.feCounselorName = local.qryClientReferralSettings.feCounselorName />
			<cfset local.feMaxMemberRefNum = local.qryClientReferralSettings.feMaxMemberRefNum />
			<cfset local.feApplicationInstanceID = local.qryClientReferralSettings.feApplicationInstanceID />
			<cfset local.fePendingStatusID = local.qryClientReferralSettings.fePendingStatusID />	
			<cfset local.feReferredStatusID = local.qryClientReferralSettings.feReferredStatusID />
			<cfset local.fePayProfileID = local.qryClientReferralSettings.fePayProfileID />
			<cfset local.feGLAccountID = local.qryClientReferralSettings.feGLAccountID />
			<cfset local.feDspSurveyOption = val(local.qryClientReferralSettings.feDspSurveyOption) />
			<cfset local.feSurveyOptionDefaultYes = val(local.qryClientReferralSettings.feSurveyOptionDefaultYes) />
			<cfset local.feDspBlogOption = val(local.qryClientReferralSettings.feDspBlogOption) />
			<cfset local.feDspQuestionTree = val(local.qryClientReferralSettings.feDspQuestionTree) />
			<cfset local.feDspClientInfoFormFirst = val(local.qryClientReferralSettings.feDspClientInfoFormFirst) />
			<cfset local.feLegalIssueDescTitle = local.qryClientReferralSettings.feLegalIssueDescTitle />
			<cfset local.feAdditionalFiltersTitle = local.qryClientReferralSettings.feAdditionalFiltersTitle />
			<cfset local.feReferralInforMatchTitle = local.qryClientReferralSettings.feReferralInforMatchTitle />
			<cfset local.feAdditionalInfoTitle = local.qryClientReferralSettings.feAdditionalInfoTitle />
			<cfset local.feFormInstructionsContentID = val(local.qryClientReferralSettings.feFormInstructionsContentID) />
			<cfset local.feFormInstructionsContent = local.qryClientReferralSettings.feFormInstructionsContent />
			<cfset local.feFormInstructionsStep1ContentID = val(local.qryClientReferralSettings.feFormInstructionsStep1ContentID) />
			<cfset local.feFormInstructionsStep1Content = local.qryClientReferralSettings.feFormInstructionsStep1Content />
			<cfset local.feFormInstructionsStep2ContentID = val(local.qryClientReferralSettings.feFormInstructionsStep2ContentID) />
			<cfset local.feFormInstructionsStep2Content = local.qryClientReferralSettings.feFormInstructionsStep2Content />
			<cfset local.feFormInstructionsStep3ContentID = val(local.qryClientReferralSettings.feFormInstructionsStep3ContentID) />
			<cfset local.feFormInstructionsStep3Content = local.qryClientReferralSettings.feFormInstructionsStep3Content />
			<cfset local.feFormInstructionsStep4ContentID = val(local.qryClientReferralSettings.feFormInstructionsStep4ContentID) />
			<cfset local.feFormInstructionsStep4Content = local.qryClientReferralSettings.feFormInstructionsStep4Content />
			<cfset local.feSuccessfulResultsInstructionsContentID = val(local.qryClientReferralSettings.feSuccessfulResultsInstructionsContentID) />
			<cfset local.feSuccessfulResultsInstructionsContent = local.qryClientReferralSettings.feSuccessfulResultsInstructionsContent />
			<cfset local.feDspLimitNumOfReferrals = val(local.qryClientReferralSettings.feDspLimitNumOfReferrals) />
			<cfset local.feMaxRefPerClient = local.qryClientReferralSettings.feMaxRefPerClient />
			<cfset local.feFreqRefPerClient = local.qryClientReferralSettings.feFreqRefPerClient />
			<cfset local.feIntakeFieldIDList =  valueList(local.qryClientReferralSettings.feIntakeFieldID) />	
			<cfset local.feDuplicateReferralTxt = local.qryClientReferralSettings.feDuplicateReferralTxt />	

			<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.feGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
			<cfset local.feGLAccountPath = len(local.tmpStrAccount.qryAccount.thePathExpanded) ? local.tmpStrAccount.qryAccount.thePathExpanded : "" />
			<cfset local.strFEGLAcctWidgetData = { label="Default Front-End Client Fees Revenue Account", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
					idFldName="feGLAccountID", idFldValue=val(local.feGLAccountID), pathFldValue=local.feGLAccountPath, pathNoneTxt="(no account selected)"}>
			<cfset local.strFEGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strFEGLAcctWidgetData)>
	
			<cfscript>
			local.referralStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID, isPending=1, isCoordination=0, canEditLawyer=1, canRefer=1);
			if(local.referralStatusResponseStruct.success){
				local.qryFrontEndPendingStatuses = local.referralStatusResponseStruct.qryStatus;
			} else {
				return showRefError('Pending CanEditLawyer CanRefer Referral Statuses are not defined correctly. Please contact administrator.');
			}
			
			local.referralStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID, isOpen=1, isReferred=1, canEditLawyer=1);
			if(local.referralStatusResponseStruct.success){
				local.qryFrontEndReferredStatuses = local.referralStatusResponseStruct.qryStatus;
			} else {
				return showRefError('Open IsReferred CanEditLawyer Referral Statuses are not defined correctly. Please contact administrator.');
			}
			local.qryGetIntakeFormFieldDetails = this.objAdminReferrals.getIntakeFormFieldDetails(referralID=this.referralID);
			</cfscript>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>			
				<cfinclude template="frm_mainSettings.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="manageClassifications" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			local.classificationListJSONLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralClassifications&mode=stream&referralID=#this.referralID#";

			// Initialize Group Set Selector Widget
			local.objGroupSetSelector = CreateObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector");
			local.strGroupSetSelector = local.objGroupSetSelector.getMultipleGroupSetSelector(
				siteID=arguments.event.getValue('mc_siteInfo.siteID'),
				orgID=arguments.event.getValue('mc_siteInfo.orgID'),
				selectorID="referralClassificationsGroupSets",
				siteResourceID=this.referralID,
				selectedGSGridHeight=300,
				editClassificationLink=buildCurrentLink(arguments.event,"editClassification")
			);

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Classifications' });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_classification.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageAgencies" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			local.agencyListJSONLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralAgencies&mode=stream&referralID=#this.referralID#";

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Agencies' });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>	
				<cfinclude template="dsp_agency.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="manageSources" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			local.sourceListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getSources&mode=stream&referralID=#this.referralID#';

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Sources' });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>	
				<cfinclude template="dsp_source.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageStatuses" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.referralID = this.referralID;
			local.referralStatusList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralStatus&mode=stream&referralID=#this.referralID#";
			local.statusUrlString = "";

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Statuses' });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>	
				<cfinclude template="dsp_status.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageSurveyTypes" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			local.surveyTypeList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getSurveyTypes&mode=stream&referralID=#this.referralID#";
			local.surveyTypeUrlString = "";

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Survey Types' });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
 				<cfinclude template="dsp_surveyTypes.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>	

	<cffunction name="manageReferralCustomFields" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");

			local.arrAttorneyFieldsGridData = [{ 
				gridext="#this.siteResourceID#_attorneyFields",
				initGridOnLoad=false,
				controllingSRID=this.siteResourceID,
				resourceType='Referrals', 
				areaName='Attorney' 
			}];
			local.strAttorneyFieldsGrid = local.objResourceCustomFields.getGridHTML(arrGridData=local.arrAttorneyFieldsGridData);

			local.arrClientRefFieldsGridData = [{ 
				gridext="#this.siteResourceID#_clientRefFields",
				initGridOnLoad=false,
				controllingSRID=this.siteResourceID,
				resourceType='ClientReferrals', 
				areaName='ClientReferrals' 
			}];
			local.strClientReferralFieldsGrid = local.objResourceCustomFields.getGridHTML(arrGridData=local.arrClientRefFieldsGridData);

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Referral Custom Fields' });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_referralCustomFields.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="listAuditLog" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			local.referralsAuditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getreferralsAuditLog&mode=stream&referralID=#this.referralID#";

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text='Audit Log' });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_auditLog.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editClassification" access="public" output="false" returntype="struct" hint="Add Sub-Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.classificationID = arguments.event.getValue('classificationID',0);
			local.qryGetClassification = this.objAdminReferrals.getClassificationByID(local.classificationID);
			local.nameOverride = local.qryGetClassification.name;
			local.referralID = this.referralID;
			local.allowSearch = local.qryGetClassification.allowSearch;
			local.showInSearchResults = local.qryGetClassification.showInSearchResults;
			// For new classifications, use groupSetID from URL parameter; for existing, use from database
			local.groupSetID = arguments.event.getValue('groupSetID', local.qryGetClassification.groupSetID);
			local.qryGroupSets = CreateObject("component","model.admin.MemberGroupSets.MemberGroupSets").getGroupSets(orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			local.saveClassificationLink = buildCurrentLink(arguments.event,"saveClassification") & "&mode=stream";
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_classification.cfm" />
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="saveClassification" access="public" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.classificationID = arguments.event.getValue('classificationID');

			if (local.classificationID)
				this.objAdminReferrals.updateClassification(arguments.event);
			else
				local.classificationID = this.objAdminReferrals.insertClassification(arguments.event);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadClassificationTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="editSurvey" access="public" output="false" returntype="struct" hint="edit survey">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();

			local.referralID = this.referralID;
			local.panelID = arguments.event.getValue('panelID');
			local.surveyID = arguments.event.getValue('surveyID',0);
			local.qryGetSurvey = this.objAdminReferrals.getSurveyByID(local.surveyID);
			local.qryGetSurveyContent = this.objAdminReferrals.getSurveyContent(local.surveyID);
			local.qryGetSurveyTypes = this.objAdminReferrals.getSurveyTypes(local.referralID);
			local.qryGetAvailableLanguages = this.objAdminReferrals.getAvailableContentLanguages(val(local.qryGetSurvey.contentID));
			local.defaultLanguageID = arguments.event.getValue('mc_siteInfo.defaultLanguageID');
			local.surveyName = local.qryGetSurvey.surveyName;
			local.isActive = local.qryGetSurvey.isActive;
			local.contentID = local.qryGetSurvey.contentID;	
			local.surveyTypeID = local.qryGetSurvey.surveyTypeID;
			local.qryGetLanguages = CreateObject("component","model.admin.website.website").getLanguages();	
			local.formLink = this.link.editSurvey;
			local.saveFormLink = buildCurrentLink(arguments.event,"saveSurvey") & "&mode=stream";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_survey.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="saveSurvey" access="public" returntype="struct" >
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.surveyID = arguments.event.getValue('surveyID');

			if (local.surveyID)
				this.objAdminReferrals.updateSurvey(arguments.event);
			else
				local.surveyID = this.objAdminReferrals.insertSurvey(arguments.event);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshSurveyGrid();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSurveyType" access="public" output="false" returntype="struct" hint="edit status">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.referralID = this.referralID;
			local.surveyTypeID = arguments.event.getValue('surveyTypeID',0);
			local.qrySurveyType = this.objAdminReferrals.getSurveyTypeByID(surveyTypeID=local.surveyTypeID);
			local.surveyTypeName = local.qrySurveyType.name;
			local.surveyFreq = local.qrySurveyType.surveyFreq;
			local.isRetainedCase = local.qrySurveyType.isRetainedCase;
			local.isClosedCase = local.qrySurveyType.isClosedCase;
			local.isModestMeans = local.qrySurveyType.isModestMeans;
			local.saveFormLink = buildCurrentLink(arguments.event,"saveSurveyType") & "&mode=stream";
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_surveyType.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="saveSurveyType" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.surveyTypeID = arguments.event.getValue('surveyTypeID',0)>

		<cfif local.surveyTypeID>
			<cfset this.objAdminReferrals.updateSurveyType(arguments.event)>
		<cfelse>
			<cfset local.surveyTypeID = this.objAdminReferrals.insertSurveyType(arguments.event)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshSurveyTypesGrid();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	
	
	<cffunction name="editAgency" access="public" output="false" returntype="struct" hint="edit survey">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.referralID = this.referralID;
			local.qryStates = application.objCommon.getStates();
			local.agencyID = arguments.event.getValue('agencyID',0);
			local.qryGetAgency = this.objAdminReferrals.getAgencyByID(local.agencyID);
			local.agencyName = local.qryGetAgency.agencyName;
			local.address1 = local.qryGetAgency.address1;
			local.address2 =  local.qryGetAgency.address2;
			local.address3 =  local.qryGetAgency.address3;
			local.city = local.qryGetAgency.city;
			local.state = local.qryGetAgency.state;
			local.postalCode = local.qryGetAgency.postalCode;
			local.email = local.qryGetAgency.email;
			local.phone = local.qryGetAgency.phone;
			local.alternatePhone = local.qryGetAgency.alternatePhone;
			local.website = local.qryGetAgency.website;
			local.description = local.qryGetAgency.description;
			local.isActive = local.qryGetAgency.isActive;
			local.importCode = local.qryGetAgency.importCode;
			local.saveFormLink = buildCurrentLink(arguments.event,"saveAgency")  & "&mode=stream";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_agency.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="saveAgency" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			if (arguments.event.getValue('agencyID'))
				this.objAdminReferrals.updateAgency(arguments.event);
			else
				var agencyID = this.objAdminReferrals.insertAgency(arguments.event);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadAgencyTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doEmailReferral" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="callUID" type="string" required="true">
		<cfargument name="clientReferralID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		
		<cfscript>
		local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.qryReferralSettings = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
		local.clientMailTopTxt = local.qryReferralSettings.clientMailTopTxt;
		local.clientMailBottomTxt = local.qryReferralSettings.clientMailBottomTxt;
		local.memberMailTopTxt = local.qryReferralSettings.memberMailTopTxt;
		local.memberMailBottomTxt = local.qryReferralSettings.memberMailBottomTxt;
		local.dspEmailPanelList = local.qryReferralSettings.dspEmailPanelList;
		local.dspNewsletterLink = local.qryReferralSettings.dspNewsletterLink;
		var currTab = arguments.event.getValue('tab','client');
		
		local.refDataParams = { orgID=arguments.event.getValue('mc_siteInfo.orgID') };
		if (arguments.clientReferralID GT 0) local.refDataParams.clientReferralID = arguments.clientReferralID;
		else local.refDataParams.callUID = arguments.callUID;

		local.qryGetReferralData = this.objAdminReferrals.getReferralData(argumentCollection=local.refDataParams);

		local.thisClientID = val(local.qryGetReferralData.clientParentID);
		if (not local.thisClientID)
			local.thisClientID = val(local.qryGetReferralData.clientID);
		local.questionAnswerPath = this.objAdminReferrals.getSearchXMLByClientID(local.thisClientID);
		local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
		local.qryFieldsetID = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='referralsearch');
		local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=this.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "http://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.clientSubscriptionLocalLink = "/?pg=clientRefSubscribe";
		local.clientSubscriptionExternalLink = "http://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.clientSubscriptionLocalLink;
		local.sentToClient = false;
		// convert times from central (how stored in db) to default timezone of site
		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));		
		local.referralDate = now();
		if (local.regTimeZone neq "US/Central") {
			local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
		}		
		</cfscript>

		<cfset local.objReferrals = CreateObject("component","model.referrals.referrals")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.thisPostTypeFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteInfo.siteID'), resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.arrThisPostTypeFields = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren>

		<cfset local.arrClientRefCustomFields = local.objReferrals.getClientReferralCustomFieldWithData(itemID=local.qryGetReferralData.clientReferralID, itemType='ClientRefCustom', arrResourceFields=local.arrThisPostTypeFields, objCustomFields=local.objResourceCustomFields)>

		<cfloop query="local.qryGetReferralData">
			<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID)>
			<cfset local.memberName = local.qryMember.firstName & " "	& local.qryMember.lastName>
			<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
			<cfset local.qryGetMemberRefEmails = this.objAdminReferrals.getMemberRefEmails(memberid=local.qryMember.memberID, referralID=this.referralID) />
			<cfset local.memberEmailList = valueList(local.qryGetMemberRefEmails.email) />			

			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />
			<cfset local.radius_dist = "" />
			<cfif len(this.emailRecipient)>
				<cfset local.emailAll = replace(this.emailRecipient,";",",","all")>
				<cfset local.sendFrom = trim(listFirst(this.emailRecipient,";"))>
			<cfelse>
				<cfset local.emailAll = "">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfif>

			<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
			<cfset local.mailCollectionReplyTo = local.sendFrom>
			<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Client Referral">
			
			<cfloop query="local.qryGetReferralFilterData">
				<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
					<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
						<cfcase value="panelid1">
							<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
							<cfset local.panelid1 = local.qryGetPanelInfo.name />
						</cfcase>
						<cfcase value="subpanelid1">
							<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
								<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
								<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
							</cfloop>
						</cfcase>
					</cfswitch>
				</cfif>
				<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
					<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
				</cfif>
			</cfloop>	

			<!--- Send referral e-mail to attorney --->
			<cfif listLen(local.memberEmailList)>
				<cfif local.qryReferralSettings.memberEmailTemplateId neq '' && local.qryReferralSettings.memberEmailTemplateId gt 0>
					
					<cfset local.memberEmailTemplateId = local.qryReferralSettings.memberEmailTemplateId>
					
					<cfset local.objTemplate = this.objAdminReferrals.getReferralEmailTemplate(local.memberEmailTemplateId)>
					<cfset local.referraldata = { 
						referralID=local.qryReferralSettings.referralID
						,siteID = arguments.event.getValue('mc_siteinfo.siteID')
						,sitename = arguments.event.getValue('mc_siteinfo.sitename')
						,orgId = arguments.event.getValue('mc_siteinfo.orgId')
						,orgcode = arguments.event.getValue('mc_siteinfo.orgcode')
						,clientID = local.qryGetReferralData.clientID
						,callUID = arguments.callUID
						,clientFirstName = local.qryGetReferralData.firstName
						,clientLastName = local.qryGetReferralData.lastName
						,clientFullName = local.qryGetReferralData.firstName &' '&local.qryGetReferralData.middleName &' '&local.qryGetReferralData.lastName
						,memberFirstName = local.qryGetReferralData.memFirstName
						,memberLastName = local.qryGetReferralData.memLastname
						,memberFullName = local.qryGetReferralData.memFirstName &' '&local.qryGetReferralData.memMiddleName &' '&local.qryGetReferralData.memLastname
						,memberCompany = local.qryGetReferralData.company
						,memberMemberNumber = local.qryGetReferralData.memberNumber
						,primaryPanelName = local.panelid1
						,clientEmail = local.qryGetReferralData.email
						,clientHomePhone= local.qryGetReferralData.homePhone
						,clientCellPhone= local.qryGetReferralData.cellPhone
						,clientAlternatePhone= local.qryGetReferralData.alternatePhone
						,clientBusiness = local.qryGetReferralData.businessName
						,clientAddress1 = local.qryGetReferralData.address1
						,clientAddress2 = local.qryGetReferralData.address2
						,clientCity = local.qryGetReferralData.city
						,clientState = local.qryGetReferralData.clientState
						,clientZipCode = local.qryGetReferralData.postalCode
						,clientLanguage = local.qryGetReferralData.languageName
						,representativeId= local.qryGetReferralData.repID
						,repFirstName= local.qryGetReferralData.repFirstName
						,repLastName= local.qryGetReferralData.repLastName
						,repEmail= local.qryGetReferralData.repEmail
						,repHomePhone= local.qryGetReferralData.repHomePhone
						,repCellPhone= local.qryGetReferralData.repCellPhone
						,repAlternatePhone= local.qryGetReferralData.repAlternatePhone
						,interviewerName= local.qryGetReferralData.interviewerName
						,clientReferralID= local.qryGetReferralData.clientReferralID
						,referralDate= local.referralDate
						,regTimeZoneName= local.regTimeZoneName
						,refIssueDesc= local.qryGetReferralData.issueDesc
						,memberExternalLink= local.memberExternalLink
						,questionAnswerPath = local.questionAnswerPath
						,transactionDate = ''
						,showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType') 
						,defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType') 
					}>

					<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
						<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
							<cfoutput>
							<cfif structKeyExists(local.thisField, "value")>
								<cfif isArray(local.thisField.value)>
									<cfloop array="#local.thisField.value#" index="local.thisItem">
										#local.thisItem#
									</cfloop>
								<cfelse>
									#local.thisField.value#
								</cfif>
							</cfif>
						</cfoutput>
						</cfsavecontent>
					</cfloop>
					
					<cfset local.strMemberReferralGrid = this.objAdminReferrals.prepareMemberReferralGrid(referraldata = local.referraldata)>
					<cfset StructInsert(local.referraldata, 'strMemberReferralGrid', local.strMemberReferralGrid,true)>
					<cfset StructInsert(local.referraldata, 'strClientReferralGrid', '' ,true)>
					<cfset StructInsert(local.referraldata, 'strClientPaymentTable', '',true)>
					
					<cfset local.strArgs = { content=local.objTemplate.rawContent, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
					<cfset local.attroneyEmailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
					<cfset local.fromName = local.objTemplate.emailFromName>
					<cfset local.fromEmail = local.objTemplate.emailFrom>
					<cfset local.subject = local.objTemplate.subjectLine>
					<cfset local.strArgs = { content=local.subject, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
					<cfset local.subject = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
					<cfset local.replyToEmail = local.objTemplate.emailFrom>
				<cfelse>
					<cfset local.attroneyEmailContent = prepareAttorneyEmailContent(
					attorneyName = local.qryGetReferralData.memberName,
					panelId = local.panelid1,
					emailTopText = local.memberMailTopTxt,
					clientFirstName = local.qryGetReferralData.firstName,
					clientLastName = local.qryGetReferralData.lastName,
					clientBusiness=local.qryGetReferralData.businessName,
					clientLanguage=local.qryGetReferralData.languageName,
					clientEmail = local.qryGetReferralData.email,
					clientHomePhone= local.qryGetReferralData.homePhone,
					clientCellPhone= local.qryGetReferralData.cellPhone,
					clientAlternatePhone= local.qryGetReferralData.alternatePhone,
					representativeId= local.qryGetReferralData.repID,
					repFirstName= local.qryGetReferralData.repFirstName,
					repLastName= local.qryGetReferralData.repLastName,
					repEmail= local.qryGetReferralData.repEmail,
					repHomePhone= local.qryGetReferralData.repHomePhone,
					repCellPhone= local.qryGetReferralData.repCellPhone,
					repAlternatePhone= local.qryGetReferralData.repAlternatePhone,interviewerName= local.qryGetReferralData.interviewerName,clientReferralID= local.qryGetReferralData.clientReferralID,
					referralDate= local.referralDate,
					regTimeZoneName= local.regTimeZoneName,
					refIssueDesc= local.qryGetReferralData.issueDesc,
					emailBottomText= local.memberMailBottomTxt,
					memberLocalLink= local.memberLocalLink,
					memberExternalLink= local.memberExternalLink,
					siteName = arguments.event.getValue('mc_siteinfo.sitename'),questionAnswerPath = local.questionAnswerPath)>
					
					<cfset local.fromName = local.qryReferralSettings.title>
					<cfset local.fromEmail = local.mailCollectionFrom>
					<cfset local.subject = local.mailCollectionSubject>
					<cfset local.replyToEmail = local.mailCollectionReplyTo>
				</cfif>
				<cftry>		
					<cfset local.sednEmailStr = this.objAdminReferrals.sendReferralEmail(referralID=local.qryReferralSettings.referralID,
					siteid=arguments.event.getValue('mc_siteinfo.siteid'),
					recordedByMemberID=local.qryGetReferralData.enteredByMemberID,
					messageContent=local.attroneyEmailContent,
					contentTitle="Referral Email",
					fromName=local.fromName,
					fromEmail=local.fromEmail,
					replyToEmail=local.replyToEmail,
					subject=local.subject,
					refMemberID=local.qryGetReferralData.memberID,
					refMemberName=local.memberName,
					refMemberEmail=this.emailRecipient,
					messageTypeCode="REFERRALMEMBER")>	
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>				
				
		</cfloop>	<!--- //loop query="local.qryGetReferralData --->

		<!--- Send referral e-mail to client --->
		<cfif ((len(trim(local.qryGetReferralData.email)) and isValid("regex",local.qryGetReferralData.email,application.regEx.email)) 
				OR (len(trim(local.qryGetReferralData.repEmail))) and isValid("regex",local.qryGetReferralData.repEmail,application.regEx.email)) and not local.sentToClient>
			<cfscript>
			local.toEmailList = "";
			if (len(trim(local.qryGetReferralData.email)) and isValid("regex",local.qryGetReferralData.email,application.regEx.email))
				local.toEmailList = local.qryGetReferralData.email;

			if (len(trim(local.qryGetReferralData.repEmail)) and isValid("regex",local.qryGetReferralData.repEmail,application.regEx.email))
				local.toEmailList = iif(len(trim(local.toEmailList)),de("#local.toEmailList#,"),de("")) & local.qryGetReferralData.repEmail;

			if (len(trim(local.emailAll)) and isValid("regex",local.emailAll,application.regEx.email))
				local.toEmailList = ListAppend(local.toEmailList, local.emailAll);
			</cfscript>
			
			<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
			<cfset local.mailCollectionReplyTo = local.sendFrom>
			<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Attorney Referral">

			<cfif local.qryReferralSettings.clientEmailTemplateId neq '' && local.qryReferralSettings.clientEmailTemplateId gt 0>
					
				<cfset local.clientEmailTemplateId = local.qryReferralSettings.clientEmailTemplateId>
				
				<cfset local.objTemplate = this.objAdminReferrals.getReferralEmailTemplate(local.clientEmailTemplateId)>
				<cfset local.referraldata = { 
					referralID=local.qryReferralSettings.referralID
					,siteID = arguments.event.getValue('mc_siteinfo.siteID')
					,sitename = arguments.event.getValue('mc_siteinfo.sitename')
					,orgId = arguments.event.getValue('mc_siteinfo.orgId')
					,orgcode = arguments.event.getValue('mc_siteinfo.orgcode')
					,clientID = local.qryGetReferralData.clientID
					,callUID = arguments.callUID
					,clientFirstName = local.qryGetReferralData.firstName
					,clientLastName = local.qryGetReferralData.lastName
					,clientFullName = local.qryGetReferralData.firstName &' '&local.qryGetReferralData.middleName &' '&local.qryGetReferralData.lastName
					,memberFirstName = local.qryGetReferralData.memFirstName
					,memberLastName = local.qryGetReferralData.memLastname
					,memberFullName = local.qryGetReferralData.memFirstName &' '&local.qryGetReferralData.memMiddleName &' '&local.qryGetReferralData.memLastname
					,memberCompany = local.qryGetReferralData.company
					,memberMemberNumber = local.qryGetReferralData.memberNumber
					,primaryPanelName = local.panelid1
					,clientEmail = local.qryGetReferralData.email
					,clientHomePhone= local.qryGetReferralData.homePhone
					,clientCellPhone= local.qryGetReferralData.cellPhone
					,clientAlternatePhone= local.qryGetReferralData.alternatePhone
					,clientBusiness = local.qryGetReferralData.businessName
					,clientAddress1 = local.qryGetReferralData.address1
					,clientAddress2 = local.qryGetReferralData.address2
					,clientCity = local.qryGetReferralData.city
					,clientState = local.qryGetReferralData.clientState
					,clientZipCode = local.qryGetReferralData.postalCode
					,clientLanguage = local.qryGetReferralData.languageName
					,representativeId= local.qryGetReferralData.repID
					,repFirstName= local.qryGetReferralData.repFirstName
					,repLastName= local.qryGetReferralData.repLastName
					,repEmail= local.qryGetReferralData.repEmail
					,repHomePhone= local.qryGetReferralData.repHomePhone
					,repCellPhone= local.qryGetReferralData.repCellPhone
					,repAlternatePhone= local.qryGetReferralData.repAlternatePhone
					,interviewerName= local.qryGetReferralData.interviewerName
					,clientReferralID= local.qryGetReferralData.clientReferralID
					,referralDate= local.referralDate
					,regTimeZoneName= local.regTimeZoneName
					,transactionDate = ''
					,refIssueDesc= local.qryGetReferralData.issueDesc
					,memberExternalLink= local.memberExternalLink
					,questionAnswerPath = local.questionAnswerPath
					,showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType') 
					,defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType') 
				}>

				<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
					<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
						<cfoutput>
						<cfif structKeyExists(local.thisField, "value")>
							<cfif isArray(local.thisField.value)>
								<cfloop array="#local.thisField.value#" index="local.thisItem">
									#local.thisItem#
								</cfloop>
							<cfelse>
								#local.thisField.value#
							</cfif>
						</cfif>
					</cfoutput>
					</cfsavecontent>
				</cfloop>
				
				<cfset local.strClientReferralGrid = this.objAdminReferrals.prepareClientReferralGrid(referraldata = local.referraldata)>
				<cfset StructInsert(local.referraldata, 'strClientReferralGrid', local.strClientReferralGrid,true)>
				<cfset StructInsert(local.referraldata, 'strMemberReferralGrid','',true)>
				<cfset StructInsert(local.referraldata, 'strClientPaymentTable','',true)>
				
				<cfset local.strArgs = { content=local.objTemplate.rawContent, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
				
				<cfset local.clientEmailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>	
				
				<cfset local.fromName = local.objTemplate.emailFromName>
				<cfset local.fromEmail = local.objTemplate.emailFrom>
				<cfset local.subject = local.objTemplate.subjectLine>
				<cfset local.strArgs = { content=local.subject, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
				<cfset local.subject = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
				<cfset local.replyToEmail = local.objTemplate.emailFrom>
			<cfelse>
				<cfset local.clientEmailContent = prepareClientReferralEmailContent(
				sitename=arguments.event.getValue('mc_siteinfo.sitename'),
				emailTopText=local.clientMailTopTxt,
				emailBottomText=local.clientMailBottomTxt,
				orgId=arguments.event.getValue('mc_siteInfo.orgID'),
				orgcode=arguments.event.getValue('mc_siteInfo.orgcode'),
				emailPanelList=local.dspEmailPanelList,
				clientId=local.thisClientID,
				callUID=arguments.callUID,
				newsLetterLink=local.dspNewsletterLink,
				clientSubscriptionExternalLink = local.clientSubscriptionExternalLink)>
				
				<cfset local.fromName = local.qryReferralSettings.title>
				<cfset local.fromEmail = local.mailCollectionFrom>
				<cfset local.subject = local.mailCollectionSubject>
				<cfset local.replyToEmail = local.mailCollectionReplyTo>
			</cfif>
			
			<cfif len(trim(local.toEmailList))>
				<cftry>					
					<cfset local.sednEmailStr = this.objAdminReferrals.sendReferralEmail(
					referralID=local.qryReferralSettings.referralID,
					siteid=arguments.event.getValue('mc_siteinfo.siteid'),
					recordedByMemberID=local.qryGetReferralData.enteredByMemberID,
					messageContent=local.clientEmailContent,
					contentTitle="Referral Email",
					fromName=local.fromName,
					fromEmail=local.fromEmail,
					replyToEmail=local.replyToEmail,
					subject=local.subject,
					refMemberID=local.qryReferralSettings.clientFeeMemberID,
					refMemberName=local.qryGetReferralData.clientName,
					refMemberEmail=local.toEmailList,
					messageTypeCode="REFERRALCLIENT")>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>				
		</cfif>
	</cffunction>
	
	<cffunction name="doEmailReopenedReferral" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" />
		<cfargument name="clientReferralID" type="numeric" required="true" />

		<cfset var local = structNew()>

		<cfscript>
		local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.qryGetReferralData = this.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.clientReferralID);
		local.thisClientID = val(local.qryGetReferralData.clientParentID);
		if (not local.thisClientID)
			local.thisClientID = val(local.qryGetReferralData.clientID);
		local.qryReferralSettings = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
		local.memberMailTopTxt = local.qryReferralSettings.memberMailTopTxt;
		local.memberMailBottomTxt = local.qryReferralSettings.memberMailBottomTxt;			
		local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
		local.qryFieldsetID = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='referralsearch');
		local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=this.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "http://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.clientSubscriptionLocalLink = "/?pg=clientRefSubscribe";
		local.clientSubscriptionExternalLink = "http://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.clientSubscriptionLocalLink;
		local.sentToClient = false;
		// convert times from central (how stored in db) to default timezone of site
		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));		
		local.referralDate = now();
		if (local.regTimeZone neq "US/Central") {
			local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
		}		
		</cfscript>

		<cfloop query="local.qryGetReferralData">
			<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID) />
			<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
			<cfset local.qryGetMemberRefEmails = this.objAdminReferrals.getMemberRefEmails(memberid=local.qryMember.memberID, referralID=this.referralID) />
			<cfset local.memberEmailList = valueList(local.qryGetMemberRefEmails.email) />			

			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />
			<cfset local.radius_dist = "" />
			<cfif len(this.emailRecipient)>
				<cfset local.emailAll = replace(this.emailRecipient,";",",","all")>
				<cfset local.sendFrom = trim(listFirst(this.emailRecipient,";"))>
			<cfelse>
				<cfset local.emailAll = "">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfif>
		
			<cfloop query="local.qryGetReferralFilterData">
				<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
					<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
						<cfcase value="panelid1">
							<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
							<cfset local.panelid1 = local.qryGetPanelInfo.name />
						</cfcase>
						<cfcase value="subpanelid1">
							<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
								<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
								<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
							</cfloop>
						</cfcase>
					</cfswitch>
				</cfif>
				<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
					<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
				</cfif>
			</cfloop>	

			<!--- Send referral e-mail to attorney --->
			<cfif listLen(local.memberEmailList)>
				<cftry>
					<cfsavecontent variable="local.emailContent">
						<cfoutput>
						<div style="#local.pageStyle#">																			
							<p style="font-size:13px;color:##333333;">Dear #local.qryGetReferralData.memberName#:</p> 
							<p style="font-size:13px;color:##333333;">The following client referral has been reopened.</p>  
							<p style="font-size:13px;color:##333333;">Client referred:</p>
							<table cellpadding="4" cellspacing="0" width="50%" border="1">
							<tr> 
								<td width="25%" style="#local.tdStyle#">Client First Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.firstName#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Last Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.lastName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Business:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.businessName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Language:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.languageName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client E-mail:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.email#</td> 
							</tr> 	
							<tr> 
								<td style="#local.tdStyle#">Client Home Phone ##:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.homePhone#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Cell Phone ##:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.cellPhone#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Alternate Phone ##:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.alternatePhone#</td> 
							</tr> 														
							<cfif val(local.qryGetReferralData.repID)>
								<tr style="#local.tdStyle#"> 
									<td width="25%" style="#local.tdStyle#">Representative First Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repFirstName#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Last Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repLastName#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative E-mail:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repEmail#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative Home Phone ##:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repHomePhone#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Cell Phone ##:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repCellPhone#</td> 
								</tr> 
																								<tr> 
									<td style="#local.tdStyle#">Representative Alternate Phone ##:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repAlternatePhone#</td> 
								</tr> 
							</cfif>	
							<tr> 
								<td style="#local.tdStyle#">Interviewer:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.interviewerName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Referral ##:</td>
								<td style="#local.tdStyle#"><a href="#local.memberExternalLink#&ra=editReferral&clientReferralID=#local.qryGetReferralData.clientReferralID#">#local.qryGetReferralData.clientReferralID#</a></td> 
							</tr>
							</tr>
							<tr>
								<td style="#local.tdStyle#">Referral Update Date:</td>
								<td style="#local.tdStyle#">#dateFormat(local.referralDate,"mm/dd/yyyy")# #timeFormat(local.referralDate,"h:mm tt")# #local.regTimeZoneName#</td>
							</tr>
							</table>
							<br>
							<div style="font-size:13px;color:##333333;">#local.memberMailBottomTxt#</div>							
						</div>
						<div style="clear:both"></div>
						</cfoutput>
					</cfsavecontent>
					
					<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

					<cfset local.arrEmailTo = []>
					<cfset local.toEmailArr = listToArray(local.memberEmailList,';')>
					<cfloop array="#local.toEmailArr#" item="local.thisEmail">
						<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
					</cfloop>

					<cfset local.emailTitle = "#arguments.event.getValue('mc_siteInfo.siteName')# Client Referral Update"/>				

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
						emailto=local.arrEmailTo,
						emailreplyto=local.sendFrom,
						emailsubject=local.emailTitle,
						emailtitle=local.emailTitle,
						emailhtmlcontent=local.emailContent,
						emailAttachments=[],
						siteID=arguments.event.getValue('mc_siteInfo.siteID'),
						memberID=arguments.event.getValue('mc_siteInfo.sysmemberid'),
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFERRALREOPEN"),
						sendingSiteResourceID=local.referralAdminSiteResourceID)/>

				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>				
				
		</cfloop>	<!--- //loop query="local.qryGetReferralData --->
	</cffunction>
	
	<cffunction name="doEmailAgencyInfo" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" />
		<cfargument name="callUID" type="string" required="true" />

		<cfset var local = structNew()>
		
		<cfscript>
		local.qryReferralSettings = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
		local.emailAgencyInfoToClientTopTxt = local.qryReferralSettings.emailAgencyInfoToClientTopTxt;
		local.emailAgencyInfoToClientBottomTxt = local.qryReferralSettings.emailAgencyInfoToClientBottomTxt;
		local.qryGetReferralData = this.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),callUID=arguments.callUID);
		local.thisClientID = val(local.qryGetReferralData.clientParentID);
		if (not local.thisClientID)
			local.thisClientID = val(local.qryGetReferralData.clientID);
		local.emailAll = trim(this.emailRecipient)
		local.qryAgencyInfo = this.objAdminReferrals.getAgencyByID(agencyID=val(local.qryGetReferralData.agencyID));		
		local.sentToClient = false;
		// convert times from central (how stored in db) to default timezone of site
		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));		
		local.referralDate = now();
		if (local.regTimeZone neq "US/Central") {
			local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
		}		
		</cfscript>	

		<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />

		<cfif len(this.emailRecipient)>
			<cfset local.emailAll = replace(this.emailRecipient,";",",","all")>
			<cfset local.sendFrom = trim(listFirst(this.emailRecipient,";"))>
		<cfelse>
			<cfset local.emailAll = "">
			<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
		</cfif>

		<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
		<cfset local.mailCollectionReplyTo = local.sendFrom>
		<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Agency Information">	

		<!--- Send referral e-mail to client --->
		<cfif ((len(trim(local.qryGetReferralData.email)) and isValid("regex",local.qryGetReferralData.email,application.regEx.email)) 
				OR (len(trim(local.qryGetReferralData.repEmail))) and isValid("regex",local.qryGetReferralData.repEmail,application.regEx.email)) and not local.sentToClient>
			<cfscript>
			local.toEmailList = "";
			if (len(trim(local.qryGetReferralData.email)) and isValid("regex",local.qryGetReferralData.email,application.regEx.email))
				local.toEmailList = local.qryGetReferralData.email;

			if (len(trim(local.qryGetReferralData.repEmail)) and isValid("regex",local.qryGetReferralData.repEmail,application.regEx.email))
				local.toEmailList = iif(len(trim(local.toEmailList)),de("#local.toEmailList#,"),de("")) & local.qryGetReferralData.repEmail;

			if (len(trim(local.emailAll)) and isValid("regex",local.emailAll,application.regEx.email))
				local.toEmailList = ListAppend(local.toEmailList, local.emailAll);
			</cfscript>
			
			<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
			<cfset local.mailCollectionReplyTo = local.sendFrom>
			<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Agency Referral">

			<cfsavecontent variable="local.clientEmailContent">
			<cfoutput>
				<html>
					<head>
						<title>#arguments.event.getValue('mc_siteInfo.siteName')# Agency Referral Confirmation</title>
					</head>
					<body>	
						<div style="#local.pageStyle#">
							<p style="font-size:13px;color:##333333;">Dear #local.qryGetReferralData.firstName#:</p> 
							<div style="font-size:13px;color:##333333; margin-botom:0;">#local.emailAgencyInfoToClientTopTxt#</div>								
							<br /><p style="font-size:13px;color:##333333; margin-top:3px;">Referral ##: #local.qryGetReferralData.clientReferralID#.</p><br />
							<table cellpadding="4" cellspacing="0" border="1" width="550">
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									Agency Name:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.agencyName#
								</td>	
							</tr>
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									Address:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.address1#
									<cfif len(trim(local.qryAgencyInfo.address2))>
										<br/>
										#local.qryAgencyInfo.address2#
									</cfif>																		
								</td>	
							</tr>
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									City:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.city#
								</td>	
							</tr>	
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									State:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.stateName#
								</td>	
							</tr>	
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									Postal Code:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.postalCode#
								</td>	
							</tr>	
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									Phone:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.phone#
								</td>	
							</tr>	
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									Alternate Phone:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.alternatePhone#
								</td>	
							</tr>	
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									E-mail:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.email#
								</td>	
							</tr>	
							<tr valign="top" style="#local.tdStyle#">
								<td width="5%" style="#local.tdStyle#">
									Website:
								</td>
								<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
									#local.qryAgencyInfo.website#
								</td>	
							</tr>																																																														
							</table>
							<br /><br />
							<div style="font-size:13px;color:##333333;">#local.emailAgencyInfoToClientBottomTxt#</div>						
						</div>												
						<div style="clear:both"></div>
					</body>
				</html>
			</cfoutput>
			</cfsavecontent>				
			
			<cfset local.fromName = local.qryReferralSettings.title>
			<cfset local.fromEmail = local.mailCollectionFrom>
			<cfset local.subject = local.mailCollectionSubject>
			<cfset local.replyToEmail = local.mailCollectionReplyTo>
			
			<cftry>					
				<cfset local.sednEmailStr = this.objAdminReferrals.sendReferralEmail(
					referralID=local.qryReferralSettings.referralID,
					siteid=arguments.event.getValue('mc_siteinfo.siteid'),
					recordedByMemberID=local.qryGetReferralData.enteredByMemberID,
					messageContent=local.clientEmailContent,
					contentTitle="Agency Referral Email",
					fromName=local.fromName,
					fromEmail=local.fromEmail,
					replyToEmail=local.replyToEmail,
					subject=local.subject,
					refMemberID=local.qryReferralSettings.clientFeeMemberID,
					refMemberName=local.qryGetReferralData.clientName,
					refMemberEmail=local.toEmailList,
					messageTypeCode="REFERRALCLIENT")>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>				
		</cfif>
	</cffunction>	

	<cffunction name="resendAgencyInfoEmail" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />
		<cfset doEmailAgencyInfo(event=arguments.event, callUID=arguments.event.getValue('callUID')) />
		<cfset local.data["msg"] = "Referral email have been sent to the selected client." />
		<cfset local.data["success"] = true />

		<cfreturn returnAppStruct(serializeJson(local.data),"echo") >		
	</cffunction>
	
	<cffunction name="doGenerateRefPrinterFriendly" access="public" returntype="struct" output="no">
		<cfargument name="Event" type="any" required="true" />
		<cfargument name="refStruct" type="struct" required="true" />

		<cfset var local = structNew() />		
		<cfset local.returnStruct = { packingSlipPath='' } />
		<cfset structAppend(local,arguments.refStruct) />
		
		<cfset local.margintop = "0.5" />
		
		<cfsavecontent variable="local.styleHeader">
			<cfoutput>
			<style type="text/css">
			*{ margin:0px; padding:0px; }
			body{ width:8in; margin:0px auto; font-size:12px; }
			.c { text-align:center; }
			.l { text-align:left; }
			.r { text-align:right; }
			.bt { border-top:1px solid ##000; }
			.bb { border-bottom:1px solid ##000; }
			.bl { border-left:1px solid ##000; }
			.br { border-right:1px solid ##000; }
			.ord1 { font-family:verdana;font-size:16px;line-height:22px;font-weight:bold;letter-spacing:2pt; }
			.ord2 { font-family:verdana;font-size:12px;line-height:16px;padding:2px 0; }
			.ord3 { font-family:verdana;font-size:12px;padding-top:8px; }
			.address { font-size:12px;font-family:verdana;font-weight:bold;line-height:16px;margin-left:20px; }
			.status { font-size:12px;font-family:verdana;font-weight:normal;line-height:16px;margin-left:20px; }
			.logo { height:180px;overflow:hidden; }
			##header h2 { font-size:20px;font-family:Verdana, Arial, Helvetica, sans-serif;font-weight:bold;line-height:40px;margin-bottom:20px; color:##0E568D;}
			##infotbl { margin-top:6px; }
			.mc_legendTitle { font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px; color:##000000; font-weight:bold; }
			.mc_bodytext { font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px; color:##000000; }
			.clientFormTbl {  font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px; color:##000000; }
			.clientFormTbl th, ##clientFormTbl td {  height:30px; }
			.clientFormTbl th.longDesc, ##clientFormTbl td.longDesc { height:78px; }
			##feesDueTbl { border: 1px solid ##cccccc; margin-left:10px; border-collapse:collapse; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px;}
			##feesDueTbl tr.ev_modern { background-color:##dddee3; }
			##feesDueTbl tr.odd_modern { background-color:##fff; }
			##feesDueTbl th { background-color:##cccccc; }
			##feesDueTbl th { height:30px; }
			##feesDueTbl td { height:20px; }
			##feesDueTbl th.longDesc, ##feesDueTbl td.longDesc { height:78px; }
			##feesDueTbl td.totals { padding-left:10px; font-weight:bold; border-top:1px solid ##CCCCCC; }
			
			##clientFeesDueTbl { border: 1px solid ##cccccc; margin-left:10px; border-collapse:collapse; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px;}
			##clientFeesDueTbl tr.ev_modern { background-color:##dddee3; }
			##clientFeesDueTbl tr.odd_modern { background-color:##fff; }
			##clientFeesDueTbl th { background-color:##cccccc; }
			##clientFeesDueTbl th { height:30px; }
			##clientFeesDueTbl td { height:20px; }
			##clientFeesDueTbl th.longDesc, ##clientFeesDueTbl td.longDesc { height:78px; }
			##clientFeesDueTbl td.totals { padding-left:10px; font-weight:bold; border-top:1px solid ##CCCCCC; }
			
			##filterTbl { border-collapse:collapse; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px;}
			.saveButton { padding: 0 35px 0 0; }
			.fr { float:right; }
			.dspTabTrue { visibility:visible; }
			.dspTabFalse { visibility:hidden; }
			fieldset.mcBox{ border:1px solid black;}
			</style>			
			</cfoutput>
		</cfsavecontent>
		
		<!--- local.ordBody styles --->
		<cfset local.pageStyle = "width:607px;padding:1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:12px;color:##000000;" />
		<cfset local.sectionTitleStyle = "line-height:1.5em; font-family:Verdana, Arial, Helvetica, sans-serif; color:##0E568D; font-weight:bold; font-size:13px;" />
		<cfset local.dataHeaderStyle = "font-weight:bold;margin-left:10px;" />
		<cfset local.dataStyle = "margin-left:30px; padding-bottom:10px;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##000000;" />			
		
		<cfsavecontent variable="local.rfHeader">
			<cfoutput>
				<html>
				<head>
					<style type="text/css">
						##header h2 { font-size:20px;font-family:Verdana, Arial, Helvetica, sans-serif;font-weight:bold;color:##0E568D;}
					</style>
				</head>
				<body>
				<div id="header">
					<h2>Referral - #local.clientReferralID# | #local.clientName#</h2>	
				</div>
				</body>
				</html>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.ordBody">
			<cfoutput>		
				<cfinclude template="dsp_clientReferral.cfm" />			
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<html>
				<head>
					#local.styleHeader#
				</head>
				<body>
					<div>
						<br />
						#local.ordBody#
					</div>
				</body>
				</html>
			</cfoutput>
		</cfsavecontent>	
		
		<cfsavecontent variable="local.rfFooter">
			<cfoutput>
			<html>
			<head></head>
			<body>
				<div style="font-family:Verdana, Arial, Helvetica, sans-serif; font-size:9px; color:##bbb; border-top:1px solid ##ccc; padding-top:7px; text-align:center;">
					<!-- cpp --> 
				</div>
			</body>
			</html>	
			</cfoutput>
		</cfsavecontent>			
		
		<!--- create a PDF --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.rfHeader } >
		<cfset local.footercol = { type="footer", evalAtPrint=true, txt=local.rfFooter } >
		<cftry>
			<cfdocument filename="#local.strFolder.folderPath#/un_#local.clientReferralID#_referral.pdf" pagetype="letter" margintop="#local.margintop#" marginbottom="0.5" marginright="0.5" marginleft="0.5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<cfdocumentsection margintop="0.25" marginbottom="0.25">
						<cfdocumentitem attributeCollection="#local.headercol#">
							#local.rfHeader#
						</cfdocumentitem>
						#local.data#
						<cfdocumentitem attributeCollection="#local.footercol#">
							#replace(local.rfFooter,'<!-- cpp -->','Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#
						</cfdocumentitem>
					</cfdocumentsection>
				</cfoutput>
			</cfdocument>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
		</cfcatch>
		</cftry>

		<!--- File Name of Referral --->
		<cfset local.fullname = rereplaceNoCase('#local.clientName#','[^A-Z0-9]','','ALL') />
		<cfset local.refFileNameNoExt = "Referral-#local.clientReferralID# #local.clientName#" />
		<cfset local.refFileNameNoExt = replace(local.refFileNameNoExt,' ','_','ALL') />
		<cfset local.refFileNameNoExt = replace(local.refFileNameNoExt,',','_','ALL') />

		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.clientReferralID#_referral.pdf","#local.strFolder.folderPath#/#local.refFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l") />
		<cfset local.returnStruct.referralFilePath = "#local.strFolder.folderPath#/#local.refFileNameNoExt#.pdf" />		
			
		<cfreturn local.returnStruct />
	</cffunction>

	<cffunction name="sendReferralCoordinationEmails" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">
		<cfargument name="callUID" type="string" required="true">

		<cfscript>
		var local = structNew();
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.objReferrals = CreateObject("component","model.referrals.referrals");

		local.qryReferralSettings = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
		
		// check email notification settings
		if (NOT val(local.qryReferralSettings.coordNtyToClientTemplateID) AND NOT val(local.qryReferralSettings.coordNtyToMemberTemplateID))
			return false;
		
		local.qryClientReferralData = this.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'), callUID=arguments.callUID);
		local.thisClientID = val(local.qryClientReferralData.clientParentID);
		if (not local.thisClientID)
			local.thisClientID = val(local.qryClientReferralData.clientID);
		
		local.sendEmailToAttorney = val(local.qryReferralSettings.coordNtyToMemberTemplateID) GT 0;
		local.qryAttorneyReferralData = QueryNew('');
		if (local.sendEmailToAttorney) {
			if (listLen(arguments.event.getValue('attorneyMemberIds',''))) {
				var attorneyMemberIds = arguments.event.getValue('attorneyMemberIds');
				local.qryAttorneyReferralData = QueryFilter(local.qryClientReferralData, 
												function(thisRow) {
													return listFind(attorneyMemberIds,val(arguments.thisRow.memberID));
												});
			} else if (arguments.event.valueExists('sendToAllAttorneys') AND NOT arguments.event.getValue('sendToAllAttorneys')) {
				local.sendEmailToAttorney = false;
			} else {
				local.qryAttorneyReferralData = local.qryClientReferralData;
			}
		}

		local.sendEmailToClient = val(local.qryReferralSettings.coordNtyToClientTemplateID) GT 0;
		if (arguments.event.valueExists('sendToClient') AND NOT arguments.event.getValue('sendToClient')) {
			local.sendEmailToClient = false;
		}

		local.questionAnswerPath = this.objAdminReferrals.getSearchXMLByClientID(local.thisClientID);
		local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);		
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=this.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "http://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		
		// convert times from central (how stored in db) to default timezone of site
		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));		
		local.referralDate = now();
		if (local.regTimeZone neq "US/Central") {
			local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
		}
		</cfscript>

		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.thisPostTypeFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteInfo.siteID'), resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.arrThisPostTypeFields = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren>
		<cfset local.arrClientRefCustomFields = local.objReferrals.getClientReferralCustomFieldWithData(itemID=local.qryClientReferralData.clientReferralID, itemType='ClientRefCustom', arrResourceFields=local.arrThisPostTypeFields, objCustomFields=local.objResourceCustomFields)>
		<cfif len(this.emailRecipient)>
			<cfset local.emailAll = replace(this.emailRecipient,";",",","all")>
			<cfset local.sendFrom = trim(listFirst(this.emailRecipient,";"))>
		<cfelse>
			<cfset local.emailAll = "">
			<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
		</cfif>
		<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		
		<cfset local.panelid1 = "">
		<cfset local.subpanelid1 = "">
		<cfset local.radius_dist = "">
		
		<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
		<cfset local.mailCollectionReplyTo = local.sendFrom>
		<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Client Referral">
		
		<cfloop query="local.qryGetReferralFilterData">
			<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
				<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
					<cfcase value="panelid1">
						<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
						<cfset local.panelid1 = local.qryGetPanelInfo.name />
					</cfcase>
					<cfcase value="subpanelid1">
						<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
							<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
							<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
						</cfloop>
					</cfcase>
				</cfswitch>
			</cfif>
			<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
				<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
			</cfif>
		</cfloop>

		<!--- Send Coordination Notifications to Member --->
		<cfif local.sendEmailToAttorney>
			<cfloop query="local.qryAttorneyReferralData">
				<cfset local.qryMember = application.objMember.getMemberInfo(local.qryAttorneyReferralData.memberID)>
				<cfset local.memberName = local.qryMember.firstName & " " & local.qryMember.lastName>
				<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
				<cfset local.qryGetMemberRefEmails = this.objAdminReferrals.getMemberRefEmails(memberid=local.qryMember.memberID, referralID=this.referralID)>
				<cfset local.memberEmailList = valueList(local.qryGetMemberRefEmails.email)>			

				<!--- Send referral e-mail to attorney --->
				<cfif listLen(local.memberEmailList)>				
					<cfset local.memberEmailTemplate = this.objAdminReferrals.getReferralEmailTemplate(templateId=local.qryReferralSettings.coordNtyToMemberTemplateID)>
					<cfset local.referraldata = { 
						referralID=local.qryReferralSettings.referralID,
						siteID = arguments.event.getValue('mc_siteinfo.siteID'),
						sitename = arguments.event.getValue('mc_siteinfo.sitename'),
						orgId = arguments.event.getValue('mc_siteinfo.orgId'),
						orgcode = arguments.event.getValue('mc_siteinfo.orgcode'),
						clientID = local.qryAttorneyReferralData.clientID,
						callUID = arguments.callUID,
						clientFirstName = local.qryAttorneyReferralData.firstName,
						clientLastName = local.qryAttorneyReferralData.lastName,
						clientFullName = local.qryAttorneyReferralData.firstName &' '&local.qryAttorneyReferralData.middleName &' '&local.qryAttorneyReferralData.lastName,
						memberFirstName = local.qryAttorneyReferralData.memFirstName,
						memberLastName = local.qryAttorneyReferralData.memLastname,
						memberFullName = local.qryAttorneyReferralData.memFirstName &' '&local.qryAttorneyReferralData.memMiddleName &' '&local.qryAttorneyReferralData.memLastname,
						memberCompany = local.qryAttorneyReferralData.company,
						memberMemberNumber = local.qryAttorneyReferralData.memberNumber,
						primaryPanelName = local.panelid1,
						clientEmail = local.qryAttorneyReferralData.email,
						clientHomePhone= local.qryAttorneyReferralData.homePhone,
						clientCellPhone= local.qryAttorneyReferralData.cellPhone,
						clientAlternatePhone= local.qryAttorneyReferralData.alternatePhone,
						clientBusiness = local.qryAttorneyReferralData.businessName,
						clientAddress1 = local.qryAttorneyReferralData.address1,
						clientAddress2 = local.qryAttorneyReferralData.address2,
						clientCity = local.qryAttorneyReferralData.city,
						clientState = local.qryAttorneyReferralData.clientState,
						clientZipCode = local.qryAttorneyReferralData.postalCode,
						clientLanguage = local.qryAttorneyReferralData.languageName,
						representativeId= local.qryAttorneyReferralData.repID,
						repFirstName= local.qryAttorneyReferralData.repFirstName,
						repLastName= local.qryAttorneyReferralData.repLastName,
						repEmail= local.qryAttorneyReferralData.repEmail,
						repHomePhone= local.qryAttorneyReferralData.repHomePhone,
						repCellPhone= local.qryAttorneyReferralData.repCellPhone,
						repAlternatePhone= local.qryAttorneyReferralData.repAlternatePhone,
						interviewerName= local.qryAttorneyReferralData.interviewerName,
						clientReferralID= local.qryAttorneyReferralData.clientReferralID,
						referralDate= local.referralDate,
						regTimeZoneName= local.regTimeZoneName,
						refIssueDesc= local.qryAttorneyReferralData.issueDesc,
						memberExternalLink= local.memberExternalLink,
						questionAnswerPath = local.questionAnswerPath,
						transactionDate = '',
						showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType'),
						defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')
					}>

					<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
						<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
							<cfoutput>
							<cfif structKeyExists(local.thisField, "value")>
								<cfif isArray(local.thisField.value)>
									<cfloop array="#local.thisField.value#" index="local.thisItem">
										#local.thisItem#
									</cfloop>
								<cfelse>
									#local.thisField.value#
								</cfif>
							</cfif>
						</cfoutput>
						</cfsavecontent>
					</cfloop>

					<cfset StructInsert(local.referraldata,'strMemberReferralGrid','',true)>
					<cfset StructInsert(local.referraldata,'strClientReferralGrid','',true)>
					<cfset StructInsert(local.referraldata,'strClientPaymentTable','',true)>
					<cfset StructInsert(local.referraldata,'strReportMemberReferralGrid','',true)>

					<!--- Member data --->
					<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteInfo.siteCode'))>
					<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteInfo.orgID, memberID=local.qryAttorneyReferralData.memberID, content="")>
			
					<cfif application.MCEnvironment eq "production">
						<cfset local.thisHostname = local.mc_siteinfo.mainHostName>
					<cfelse>
						<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
					</cfif>
					<cfset local.tempMemberData = { memberid=local.qryAttorneyReferralData.memberID, firstName=local.qryAttorneyReferralData.memFirstName, 
													middleName=local.qryAttorneyReferralData.memMiddleName, lastName=local.qryAttorneyReferralData.memLastName, 
													company=local.qryAttorneyReferralData.Company, suffix=local.qryAttorneyReferralData.Suffix, 
													professionalsuffix=local.qryAttorneyReferralData.professionalsuffix, 
													prefix=local.qryAttorneyReferralData.Prefix, membernumber=local.qryAttorneyReferralData.membernumber, 
													orgcode=local.mc_siteInfo.orgcode, siteID=local.mc_siteInfo.siteID,
													hostname=local.thisHostname, useRemoteLogin=local.mc_siteInfo.useRemoteLogin}>
					<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
						<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
							<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.qryMemberFields[local.thisColumn.Name][1],true)>
						</cfif>
					</cfloop>

					<cfset local.strClientReferralGrid = this.objAdminReferrals.prepareClientReferralGrid(referraldata = local.referraldata)>
					<cfset local.referraldata.strClientReferralGrid = local.strClientReferralGrid>
					<cfset local.strMemberReferralGrid = this.objAdminReferrals.prepareMemberReferralGrid(referraldata = local.referraldata)>
					<cfset local.referraldata.strMemberReferralGrid = local.strMemberReferralGrid>
					<cfset local.strClientPaymentTable = this.objAdminReferrals.prepareClientPaymentTable(referraldata = local.referraldata)>
					<cfset local.referraldata.strClientPaymentTable = local.strClientPaymentTable>
					<cfset local.strReportMemberReferralGrid = this.objAdminReferrals.prepareReportMemberReferralGrid(referraldata = local.referraldata, memberID=local.qryAttorneyReferralData.memberID)>
					<cfset local.referraldata.strReportMemberReferralGrid = local.strReportMemberReferralGrid>
					
					<cfset local.strArgs = { content=local.memberEmailTemplate.rawContent, memberdata=local.tempMemberData, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
					<cfset local.emailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
					
					<cfset local.fromName = local.memberEmailTemplate.emailFromName>
					<cfset local.fromEmail = local.memberEmailTemplate.emailFrom>
					<cfset local.subject = local.memberEmailTemplate.subjectLine>

				
					<cfset local.strArgs = { content=local.subject, memberdata=local.tempMemberData, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
					<cfset local.subject = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
					<cfset local.replyToEmail = local.memberEmailTemplate.emailFrom>

					<cftry>		
						<cfset local.sendEmailStr = this.objAdminReferrals.sendReferralEmail(referralID=local.qryReferralSettings.referralID, siteid=arguments.event.getValue('mc_siteinfo.siteid'),
														recordedByMemberID=local.qryAttorneyReferralData.enteredByMemberID, messageContent=local.emailContent, contentTitle="Referral Email", fromName=local.fromName,
														fromEmail=local.fromEmail, replyToEmail=local.replyToEmail, subject=local.subject, refMemberID=local.qryAttorneyReferralData.memberID,
														refMemberName=local.memberName, refMemberEmail=this.emailRecipient, messageTypeCode="REFERRALMEMBER")>
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
				</cfif>
			</cfloop>
		</cfif>

		<!--- Send Coordination Notifications to Client --->
		<cfif local.sendEmailToClient
			AND (
				(len(trim(local.qryClientReferralData.email)) AND isValid("regex",local.qryClientReferralData.email,application.regEx.email)) 
				OR (len(trim(local.qryClientReferralData.repEmail))) AND isValid("regex",local.qryClientReferralData.repEmail,application.regEx.email)
			)>
			
			<cfscript>
			local.toEmailList = "";
			if (len(trim(local.qryClientReferralData.email)) and isValid("regex",local.qryClientReferralData.email,application.regEx.email))
				local.toEmailList = local.qryClientReferralData.email;

			if (len(trim(local.qryClientReferralData.repEmail)) and isValid("regex",local.qryClientReferralData.repEmail,application.regEx.email))
				local.toEmailList = iif(len(trim(local.toEmailList)),de("#local.toEmailList#,"),de("")) & local.qryClientReferralData.repEmail;

			if (len(trim(local.emailAll)) and isValid("regex",local.emailAll,application.regEx.email))
				local.toEmailList = ListAppend(local.toEmailList, local.emailAll);
			</cfscript>
			
			<cfif len(trim(local.toEmailList))>
				<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
				<cfset local.mailCollectionReplyTo = local.sendFrom>
				<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Attorney Referral">
				<cfset local.clientEmailTemplate = this.objAdminReferrals.getReferralEmailTemplate(templateId=local.qryReferralSettings.coordNtyToClientTemplateID)>
				<cfset local.referraldata = { 
					referralID=local.qryReferralSettings.referralID,
					siteID = arguments.event.getValue('mc_siteinfo.siteID'),
					sitename = arguments.event.getValue('mc_siteinfo.sitename'),
					orgId = arguments.event.getValue('mc_siteinfo.orgId'),
					orgcode = arguments.event.getValue('mc_siteinfo.orgcode'),
					clientID = local.qryClientReferralData.clientID,
					callUID = arguments.callUID,
					clientFirstName = local.qryClientReferralData.firstName,
					clientLastName = local.qryClientReferralData.lastName,
					clientFullName = local.qryClientReferralData.firstName &' '&local.qryClientReferralData.middleName &' '&local.qryClientReferralData.lastName,
					memberFirstName = local.qryClientReferralData.memFirstName,
					memberLastName = local.qryClientReferralData.memLastname,
					memberFullName = local.qryClientReferralData.memFirstName &' '&local.qryClientReferralData.memMiddleName &' '&local.qryClientReferralData.memLastname,
					memberCompany = local.qryClientReferralData.company,
					memberMemberNumber = local.qryClientReferralData.memberNumber,
					primaryPanelName = local.panelid1,
					clientEmail = local.qryClientReferralData.email,
					clientHomePhone= local.qryClientReferralData.homePhone,
					clientCellPhone= local.qryClientReferralData.cellPhone,
					clientAlternatePhone= local.qryClientReferralData.alternatePhone,
					clientBusiness = local.qryClientReferralData.businessName,
					clientAddress1 = local.qryClientReferralData.address1,
					clientAddress2 = local.qryClientReferralData.address2,
					clientCity = local.qryClientReferralData.city,
					clientState = local.qryClientReferralData.clientState,
					clientZipCode = local.qryClientReferralData.postalCode,
					clientLanguage = local.qryClientReferralData.languageName,
					representativeId= local.qryClientReferralData.repID,
					repFirstName= local.qryClientReferralData.repFirstName,
					repLastName= local.qryClientReferralData.repLastName,
					repEmail= local.qryClientReferralData.repEmail,
					repHomePhone= local.qryClientReferralData.repHomePhone,
					repCellPhone= local.qryClientReferralData.repCellPhone,
					repAlternatePhone= local.qryClientReferralData.repAlternatePhone,
					interviewerName= local.qryClientReferralData.interviewerName,
					clientReferralID= local.qryClientReferralData.clientReferralID,
					referralDate= local.referralDate,
					regTimeZoneName= local.regTimeZoneName,
					transactionDate = '',
					refIssueDesc= local.qryClientReferralData.issueDesc,
					memberExternalLink= local.memberExternalLink,
					questionAnswerPath = local.questionAnswerPath,
					showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType'),
					defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType') 
				}>

				<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
					<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
						<cfoutput>
						<cfif structKeyExists(local.thisField, "value")>
							<cfif isArray(local.thisField.value)>
								<cfloop array="#local.thisField.value#" index="local.thisItem">
									#local.thisItem#
								</cfloop>
							<cfelse>
								#local.thisField.value#
							</cfif>
						</cfif>
					</cfoutput>
					</cfsavecontent>
				</cfloop>
				
				<cfset local.strClientReferralGrid = this.objAdminReferrals.prepareClientReferralGrid(referraldata = local.referraldata)>
				<cfset StructInsert(local.referraldata, 'strClientReferralGrid', local.strClientReferralGrid,true)>
				<cfset StructInsert(local.referraldata, 'strMemberReferralGrid','',true)>
				<cfset StructInsert(local.referraldata, 'strClientPaymentTable','',true)>
				
				<cfset local.strArgs = { content=local.clientEmailTemplate.rawContent, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
				
				<cfset local.clientEmailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>	
				
				<cfset local.fromName = local.clientEmailTemplate.emailFromName>
				<cfset local.fromEmail = local.clientEmailTemplate.emailFrom>
				<cfset local.subject = local.clientEmailTemplate.subjectLine>
				<cfset local.strArgs = { content=local.subject, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
				<cfset local.subject = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
				<cfset local.replyToEmail = local.clientEmailTemplate.emailFrom>
			
				<cftry>
					<cfset local.sendEmailStr = this.objAdminReferrals.sendReferralEmail(referralID=local.qryReferralSettings.referralID, siteid=arguments.event.getValue('mc_siteinfo.siteid'),
													recordedByMemberID=local.qryClientReferralData.enteredByMemberID, messageContent=local.clientEmailContent, contentTitle="Referral Email",
													fromName=local.fromName, fromEmail=local.fromEmail, replyToEmail=local.replyToEmail, subject=local.subject,
													refMemberID=local.qryReferralSettings.clientFeeMemberID, refMemberName=local.qryClientReferralData.clientName, 
													refMemberEmail=local.toEmailList, messageTypeCode="REFERRALCLIENT")>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>
		</cfif>

		<cfreturn true>
	</cffunction>
	
	<cffunction name="viewCaseStatement" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew() />	
		
		<cfset local.clientReferralID = arguments.event.getValue('clientReferralID','0') />
		<cfset local.qryGetReferralData = this.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=local.clientReferralID) />
		<cfset local.qryGetCaseFees = this.objAdminReferrals.getCaseFees(caseID=local.qryGetReferralData.caseID) />
		<cfset local.objReferrals	= CreateObject("component","model.referrals.referrals") />
		<cfset local.qryGetCaseFeesTotals = local.objReferrals.getFeesTotals(qryItems=local.qryGetCaseFees) />
		<cfset arguments.event.paramValue('mainurl','/?pg=referrals')>
		<cfset local.mainurl = arguments.event.getValue('mainurl')>				

		<cfswitch expression="#arguments.event.getValue('refaction','')#">
			<cfcase value="emailCase">
				<cfif local.qryGetReferralData.recordcount and isValid("regex",arguments.event.getValue('_email',''),application.regEx.email)>
					<cfset local.objReferrals.doEmailCaseStatement(event=arguments.event) />
				</cfif>
				<cfsavecontent variable="local.data">
				<cfoutput>
					<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					top.$('##MCModalBody').html('<div style="margin:10px;"><div class="alert alert-success"><span class="mr-2"><i class="fa-solid fa-circle-check"></i></span>E-mail sent successfully.</div></div>');
					setTimeout(function(){ top.MCModalUtils.hideModal(); },5000);
					</script>					
				</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfcase value="loadStatement">
				<cfset local.returnPDFStruct = local.objReferrals.doGenerateCaseStatement(event=arguments.event, refStruct=local) />
				<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnPDFStruct.referralFilePath, displayName=ListLast(local.returnPDFStruct.referralFilePath,"/"), deleteSourceFile=1)>
			</cfcase>							
			<cfdefaultcase>
				<cfif not local.qryGetReferralData.recordcount>
					<cfsavecontent variable="local.data">
					<cfoutput>
						<script language="javascript">
						top.$("##MCModalFooter").toggleClass('d-flex d-none');
						</script>
						<div class="p-3">
							<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-warning mb-0" role="alert">
								<span>That Case was not found.</span>
							</div>
						</div>					
					</cfoutput>
					</cfsavecontent>
					<cfreturn returnAppStruct(local.data,"echo")>
				</cfif>
				<cfset local.memberEmail = application.objMember.getMainEmail(memberid=local.qryGetReferralData.memberID).email />								
				<cfsavecontent variable="local.data">
					<cfoutput>
						<cfinclude template="frm_emailCaseStatement.cfm" />
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>		

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewProgressReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

				
		<cfset var local = structNew() />	
		<cfset local.objReferrals	= CreateObject("component","model.referrals.referrals") />
		<cfset local.memberID = arguments.event.getValue('memberID') />
		<cfset local.qryGetMemberReferrals = local.objReferrals.getMemberReferrals(siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID, isReport=1)/>
		<cfset arguments.event.paramValue('mainurl','?pg=referrals') />
		<cfset local.mainurl = arguments.event.getValue('mainurl') />				
		<cfswitch expression="#arguments.event.getValue('refaction','')#">
		
			<cfcase value="emailProgressReport">
				<cfif local.qryGetMemberReferrals.recordcount and (isValid("regex",arguments.event.getValue('_email',''),application.regEx.email) OR len(arguments.event.getValue('_emailTypeEmail','')))>
					<cfset local.objReferrals.doEmailProgressReport(event=arguments.event) />
				</cfif>
				<cfsavecontent variable="local.data">
				<cfoutput>
					<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					top.$('##MCModalBody').html('<div style="margin:10px;"><div class="alert alert-success">E-mail sent successfully.</div></div>');
					</script>
										
				</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfcase value="loadProgressReport">
				<cfset local.returnPDFStruct = local.objReferrals.doGenerateReferralReport(orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID)/>						
				<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnPDFStruct.referralFilePath, displayName=ListLast(local.returnPDFStruct.referralFilePath,"/"), deleteSourceFile=1)>
			</cfcase>							
			<cfdefaultcase>
				<cfif not local.qryGetMemberReferrals.recordcount>
					<cfsavecontent variable="local.data">
					<cfoutput>
						<script language="javascript">
						top.$("##MCModalFooter").toggleClass('d-flex d-none');
						</script>
						<div class="p-3">
							<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-warning mb-0" role="alert">
								<span>That progress report was not found</span>
							</div>
						</div>					
					</cfoutput>
					</cfsavecontent>
					<cfreturn returnAppStruct(local.data,"echo")>
				</cfif>
				
				<cfset local.qryMemberEmails = application.objMember.getMemberEmails(memberid=local.memberID,orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<cfinclude template="frm_emailProgressReport.cfm" />
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>		

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>		
	
	<cffunction name="editSource" access="public" output="false" returntype="struct" hint="edit source">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.errmsg="";
			local.referralID = this.referralID;
			local.sourceID = arguments.event.getValue('sourceID',0);
			local.importcode=arguments.event.getTrimValue('importCode','');	
			local.qryGetSource = this.objAdminReferrals.getSourceByID(sourceID=local.sourceID);
			local.sourceName = local.qryGetSource.sourceName; 
			local.importCode = local.qryGetSource.importCode;
			local.isDefaultSource = local.qryGetSource.isDefaultSource;
			local.isCopyDefault = local.qryGetSource.isCopyDefault;
			local.isActive =  local.qryGetSource.isActive;
			local.isReferral = local.qryGetSource.isReferral;
			local.isFrontEndDefault = local.qryGetSource.isFrontEndDefault;			
			local.pageTitle = "Add Source";
			if(local.sourceID)
				local.pageTitle = "Edit Source";
			local.formLink = this.link.editSource;
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_source.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="editFeeType" access="public" output="false" returntype="struct" hint="edit fee type">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			
			local.referralID = this.referralID;
			local.typeID = arguments.event.getValue('typeID',0);
			local.formAction = this.link.saveFeeType;
			local.qryGetFeeType = this.objAdminReferrals.getFeeTypeByID(typeID=local.typeID);
			local.feeTypeName = local.qryGetFeeType.feeTypeName;
			local.isDefault = local.qryGetFeeType.isDefault;
			local.isActive =  local.qryGetFeeType.isActive;
			local.formLink = this.link.editFeeType;
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_feeType.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="saveFeeType" access="public" returntype="struct" >
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.data = "";
			local.typeID = arguments.event.getValue('typeID');

			if (local.typeID)
				this.objAdminReferrals.updateFeeType(arguments.event);
			else
				local.typeID = this.objAdminReferrals.insertFeeType(arguments.event);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadFeeTypesTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />		
	</cffunction>
	
	<cffunction name="prepareClientReferralEmailContent" hint="I prepare client email content" access="private" output="false" returntype="string">
		<cfargument name="sitename" type="string" required="yes">
		<cfargument name="emailTopText" type="string" required="yes">
		<cfargument name="orgId" type="numeric" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="emailPanelList" type="string" required="yes">
		<cfargument name="clientID" type="numeric" required="yes">
		<cfargument name="callUID" type="string" required="yes">
		<cfset var local = structNew()>

		<cfset local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets")>
		<cfset local.panelid1 = "" />
		<cfset local.subpanelid1 = "" />
		<cfset local.radius_dist = "" />
		<cfset local.emailAll = trim(this.emailRecipient) />
		
		<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />		
		
		<cfset local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=arguments.clientID)>
		<cfloop query="local.qryGetReferralFilterData">
			<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
				<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
					<cfcase value="panelid1">
						<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
						<cfset local.panelid1 = local.qryGetPanelInfo.name />
					</cfcase>
					<cfcase value="subpanelid1">
						<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
							<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
							<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
						</cfloop>
					</cfcase>
				</cfswitch>
			</cfif>
			<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
				<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
			</cfif>
		</cfloop>
		
		<cfset local.qryMemberReferralData = this.objAdminReferrals.getReferralData(orgID=arguments.orgID,callUID=arguments.callUID)>
		<cfset local.qryResultFieldsetID = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='clientemail')>		
		<cfset local.qryGetAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgid, includeTags=1)>
		<cfset local.sentToClient = true>
		
		<cfsavecontent variable="local.clientEmailContent">
		<cfoutput>
			<html>
				<head>
					<title>#arguments.sitename# Referral Confirmation</title>
				</head>
				<body>	
					<div style="#local.pageStyle#">
						<p style="font-size:13px;color:##333333;">Dear #local.qryMemberReferralData.firstName#:</p> 
						<div style="font-size:13px;color:##333333; margin-botom:0;">#arguments.emailTopText#</div>				
						<cfloop query="local.qryMemberReferralData">
							<!--- combine address fields if there are any --->
							<cfset local.arrMemberAddresses = arrayNew(1) />
							<cfset local.thisMember = this.objAdminReferrals.getMember(referralID=this.referralID, memberid=val(local.qryMemberReferralData.memberid), orgID=arguments.orgID, getVwMemberdata=1, siteResourceID=this.siteResourceID) />						
						
							<cfif local.thisMember.qryViewMemberData.recordCount>
								<cfset local.xmlResultFields = xmlParse(local.thisMember.qryViewMemberData.mc_outputFieldsXML[1])>
								<cfset local.mc_combinedAddresses = structNew()>
								<cfset local.strOrgAddressTypes = structNew()>
								<cfloop query="local.qryGetAddressTypes">
									<cfif local.qryGetAddressTypes.isTag is 1>
										<cfset local.strOrgAddressTypes["t#local.qryGetAddressTypes.addressTypeID#"] = local.qryGetAddressTypes.addressType>
									<cfelse>
										<cfset local.strOrgAddressTypes[local.qryGetAddressTypes.addressTypeID] = local.qryGetAddressTypes.addressType>
									</cfif>
								</cfloop>

								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
								<cfloop array="#local.tmp#" index="local.thisField">
									<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>

									<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
										<cfset local.strKey = "t#local.thisATID#">
									<cfelse>
										<cfset local.strKey = local.thisATID>
									</cfif>
									<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
										<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey], id=local.thisATID } >
									</cfif>
								</cfloop>

								<cfloop collection="#local.mc_combinedAddresses#" item="local.thisATID">
									<cfset local.MAfcPrefix = "ma_#local.thisATID#_" />
									<cfset local.MPfcPrefix = "mp_#local.thisATID#_" />				
									<cfif left(local.thisATID,1) eq "t">
										<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_" />
										<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_" />
									</cfif>

									<cfsavecontent variable="local.thisATFull">
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>				
										<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]#<br/></cfif>
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
										<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]#<br/></cfif>										
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
										<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]#<br/></cfif>										
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
										<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]# </cfif>										
										<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
										<cfif arrayLen(local.tmp2) is 1 and len(local.thisMember.qryViewMemberData[local.tmp2[1].xmlAttributes.fieldLabel][1])>
											<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>, </cfif>#local.thisMember.qryViewMemberData[local.tmp2[1].xmlAttributes.fieldLabel][1]# 
										</cfif>										
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
										<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])> #local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]# </cfif>										
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
										<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])><br/>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]#</cfif>
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
										<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])><br/>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]#</cfif>
									</cfsavecontent>
									<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
									<cfif left(local.thisATfull,2) eq ", ">
										<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
									</cfif>
									<cfif len(local.thisATfull)>
										<cfset local.mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
									<cfelse>
										<cfset structDelete(local.mc_combinedAddresses,local.thisATID,false)>
									</cfif>

									<cfif len(trim(local.thisATfull))>
										<cfset local.tmpStr = structNew()/>
										<cfset local.tmpStr.addressTypeID = local.mc_combinedAddresses[local.thisATID].id />
										<cfset local.tmpStr.addressType = local.mc_combinedAddresses[local.thisATID].type />
										<cfset local.tmpStr.stAddress = local.thisATfull />
											
										<cfsavecontent variable="local.stAddressMaps">
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>				
											<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]# </cfif>
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
											<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>, #local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]# </cfif>											
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
											<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>, #local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]# </cfif>											
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
											<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>, #local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]#</cfif>											
											<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
											<cfif arrayLen(local.tmp2) is 1 and len(local.thisMember.qryViewMemberData[local.tmp2[1].xmlAttributes.fieldLabel][1])>
												<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>, </cfif>#local.thisMember.qryViewMemberData[local.tmp2[1].xmlAttributes.fieldLabel][1]# 
											</cfif>											
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
											<cfif arrayLen(local.tmp) is 1 and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])> #local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]# </cfif>
										</cfsavecontent>

										<!--- Google Maps --->
										<cfset local.tmpStr.stAddressMaps = replace(local.stAddressMaps,' ,',',','ALL') />
										<cfset local.tmpStr.stGoogleMap = "" />
										<cfif len(local.tmpStr.stAddressMaps)>
											<cfset local.tmpStr.stGoogleMap = "http://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&q=#URLEncodedFormat(local.tmpStr.stAddressMaps)#&z=16" />
										</cfif>

										<!--- phones --->
										<cfset local.arrPhoneIndex = 1 />
										<cfset local.tmpStr.arrPhone =  arrayNew(1) />
										<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
											<cfif ReFindNoCase('#local.MPfcPrefix#[0-9]+',local.thisField.xmlattributes.fieldcode) and len(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
												<cfset local.tmpStr.arrPhone[local.arrPhoneIndex] = structNew() />
												<cfset local.tmpStr.arrPhone[local.arrPhoneIndex].fieldLabel =  htmlEditFormat(local.thisField.xmlattributes.fieldLabel) />
												<cfset local.tmpStr.arrPhone[local.arrPhoneIndex].phone =  local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1] />
												<cfset local.arrPhoneIndex ++ />
											</cfif>
										</cfloop> 
										<cfset arrayAppend(local.arrMemberAddresses, local.tmpStr) />
									</cfif>
									<cfset structDelete(local, "tmpStr")/>
								</cfloop>							  									
							
								<br><p style="font-size:13px;color:##333333; margin-top:3px;">Referral ##: #local.qryMemberReferralData.clientReferralID#.</p><br />
								<table cellpadding="4" cellspacing="0" border="1" width="550">
								<tr valign="top" style="#local.tdStyle#">
									<td width="5%" style="#local.tdStyle#">
										<cfset local.imgToUse = '<img src="http://#application.objPlatform.getCurrentHostname()#/assets/common/images/directory/default.jpg">'>
										<cfif local.thisMember.qryMember.hasMemberPhotoThumb is 1>
											<cfset local.imgToUse = '<img src="http://#application.objPlatform.getCurrentHostname()#/memberphotosth/#LCASE(local.qryMemberReferralData.memberNumber)#.jpg">'>
										</cfif>		
										#local.imgToUse#
									</td>
									<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
										<b>#iif(len(trim(local.qryMemberReferralData.prefix)),de("#local.qryMemberReferralData.prefix#"),de(""))# 
										#local.qryMemberReferralData.memberName# 
										#iif(len(trim(local.qryMemberReferralData.suffix)),de("#local.qryMemberReferralData.suffix#"),de(""))#</b><br/>
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_company']")>
										<cfif arrayLen(local.tmp) and len(local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>#local.thisMember.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]#<br/></cfif>
										<br/>
										<cfloop array="#local.arrMemberAddresses#"  index="local.thisItem">
											<b style="font-size:95%; margin-right:15px;">#local.thisItem.addressType#</b> 
											<cfif len(local.thisItem.stAddressMaps)><a href="#local.thisItem.stGoogleMap#" target="_blank" title="[View Map at Google]"><img src="http://#application.objPlatform.getCurrentHostname()#/assets/common/images/map.png" alt="[View Map at Google]" width="16" height="16" border="0" align="absmiddle"></a></cfif><br/>
											#local.thisItem.stAddress#<br/>
											<cfif isArray(local.thisItem.arrPhone) and arrayLen(local.thisItem.arrPhone)>
												<cfloop array="#local.thisItem.arrPhone#"  index="local.thisPhoneItem">
													<cfif structKeyExists(local.thisPhoneItem, "fieldLabel") and structKeyExists(local.thisPhoneItem, "phone")>
														#local.thisPhoneItem.fieldLabel#: #local.thisPhoneItem.phone#<br/>
													</cfif>
												</cfloop>
											</cfif>
											<br/>
										</cfloop>

										<!--- emails --->
										<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
											<cfif (ReFindNoCase('me_[0-9]+_email',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('met_[0-9]+_email',local.thisField.xmlattributes.fieldcode)) and len(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
												#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: <a href="mailto:#local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]#">#local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]#</a><br/>
											</cfif>
										</cfloop>

										<!--- websites --->
										<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
											<cfif ReFindNoCase('mw_[0-9]+_website',local.thisField.xmlattributes.fieldcode) and len(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
												#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: <a href="#local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]#" target="_blank">#local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]#</a><br/>
											</cfif>
										</cfloop>
										
										<!--- professional licenses --->
										<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
											<cfif ReFindNoCase('mpl_[0-9]+_[a-z]+',local.thisField.xmlattributes.fieldcode) and len(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
												<cfif listFindNoCase(local.thisField.xmlattributes.fieldcode,"activeDate","_")>
													#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: #dateFormat(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1], "mm/dd/yyyy")#<br/>
												<cfelse>
													#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: #local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]#<br/>
												</cfif>
											</cfif>
										</cfloop>
										
										<!--- get recordtype if available --->
										<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
										<cfif arrayLen(local.RecordTypeInFS) is 1 and local.thisMember.qryRecordType.recordCount and len(local.thisMember.qryRecordType[local.RecordTypeInFS[1].xmlAttributes.fieldLabel][1])>
											<cfloop array="#local.RecordTypeInFS[1].XMlChildren#" index="local.thisOpt"> 
												<cfif listFindNoCase(local.thisMember.qryRecordType[local.RecordTypeInFS[1].xmlAttributes.fieldLabel][1],local.thisOpt.xmlAttributes.valueID)>
													#htmlEditFormat(local.RecordTypeInFS[1].xmlAttributes.FieldLabel)#: #local.thisOpt.xmlAttributes.columnValueString#<br/>
													<cfbreak />
												</cfif>
											</cfloop>
										</cfif>

										<!--- get last login date if available --->
										<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>
										<cfif arrayLen(local.LastLoginDateInFS) is 1>
											<cfif not structKeyExists(local,"objMember")>
												<cfset local.objMember = createObject("component","model.admin.members.members")>
											</cfif>
											<cfset local.mc_dateLastLogin = local.objMember.getMember_lastLogin(memberID=val(local.qryMemberReferralData.memberid), siteID=val(GetToken(local.LastLoginDateInFS[1].xmlAttributes.fieldLabel,3,'_')))>
											<cfif len(local.mc_dateLastLogin)>
												#htmlEditFormat(local.LastLoginDateInFS[1].xmlAttributes.FieldLabel)#: #local.mc_dateLastLogin#
											</cfif>
										</cfif>
										
										<!--- get membertypeid if available --->
										<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
										<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.thisMember.qryMember[local.memberTypeInFS[1].xmlAttributes.fieldLabel][1])>
											<cfloop array="#local.memberTypeInFS[1].XMlChildren#" index="local.thisOpt"> 
												<cfif listFindNoCase(local.thisMember.qryMember[local.memberTypeInFS[1].xmlAttributes.fieldLabel][1],local.thisOpt.xmlAttributes.valueID)>
													#htmlEditFormat(local.memberTypeInFS[1].xmlAttributes.FieldLabel)#: #local.thisOpt.xmlAttributes.columnValueString#<br/>
													<cfbreak />
												</cfif>
											</cfloop>					
										</cfif>
										
										<!--- get status if available --->
										<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
										<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.thisMember.qryMember[local.memberStatusInFS[1].xmlAttributes.fieldLabel][1])>
											<cfloop array="#local.memberStatusInFS[1].XMlChildren#" index="local.thisOpt"> 
												<cfif listFindNoCase(local.thisMember.qryMember[local.memberStatusInFS[1].xmlAttributes.fieldLabel][1],local.thisOpt.xmlAttributes.valueID)>
													#htmlEditFormat(local.memberStatusInFS[1].xmlAttributes.FieldLabel)#: #local.thisOpt.xmlAttributes.columnValueString#<br/>
													<cfbreak />
												</cfif>
											</cfloop>
										</cfif>													

										<!--- memberdata --->
										<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
											<cfif ReFindNoCase('md_[0-9]+',local.thisField.xmlattributes.fieldcode) and len(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
												#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: 
												<cfif local.thisField.xmlAttributes.allowMultiple is 1>
													#ReplaceNoCase(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1],"|",", ","ALL")#<br/>
												<cfelse>
													<cfif local.thisField.xmlattributes.displayTypeCode EQ "DATE">
														#dateFormat(local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1], "mm/dd/yyyy")#<br/>
													<cfelse>
														#local.thisMember.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]#<br/>
													</cfif>
												</cfif>
											</cfif>
										</cfloop>
									</td>
									
									<cfset local.panelListContent='' >
									<cfif ListFindNoCase("S,A",arguments.emailPanelList) GT 0>
										<cfswitch expression="#arguments.emailPanelList#">										
											<cfcase value="S">
												<cfsavecontent variable="local.panelListContent">
													
														<cfset local.panelid1Content = "">
														<cfset local.panelid2Content = "">
														<cfset local.panelid3Content = "">
														<cfset local.subPanelid1Content = "">
														<cfset local.subPanelid2Content = "">
														<cfset local.subPanelid3Content = "">
														<cfloop query="local.qryGetReferralFilterData">
															<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
																<cfswitch expression="#LCase(local.qryGetReferralFilterData.elementID)#">
																	<cfcase value="panelid1,panelid2,panelid3">
																		<cfif local.qryGetReferralFilterData.elementValue GT 0>
																			<cfset local.selectedPanelid =  local.qryGetReferralFilterData.elementValue />
																			<cfif val(local.selectedPanelid)>
																				<cfset local.qryGetSelectedPrimPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.selectedPanelid) />
																				
																				<cfset local.primSelectedPanelName = local.qryGetSelectedPrimPanelInfo.name />
																				<cfset "local.#LCase(local.qryGetReferralFilterData.elementID)#Content" = "- #local.primSelectedPanelName#<br>">
																			</cfif>
																		</cfif>
																	</cfcase>
																	
																	<cfcase value="subpanelid1,subpanelid2,subpanelid3">
																		<cfset local.selectedSubPanelid =  local.qryGetReferralFilterData.elementValue />
																		<cfloop list="#local.selectedSubPanelid#" index="local.item">
																			<cfif val(local.item)>
																				<cfset local.qryGetSelectedPrimSubPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.item) />
																				<cfset local.primSelectedSubPanelName = local.qryGetSelectedPrimSubPanelInfo.name />
																				
																				<cfset "local.#LCase(local.qryGetReferralFilterData.elementID)#Content" = local[LCase(local.qryGetReferralFilterData.elementID) & "Content"] & "&nbsp;&nbsp;&nbsp;&nbsp;- #local.primSelectedSubPanelName#<br>">
																			</cfif>
																		</cfloop>
																	</cfcase>
																</cfswitch>
															</cfif>
														</cfloop>
														<cfset local.panelid1Content = local.panelid1Content & local.subPanelid1Content >
														<cfset local.panelid2Content = local.panelid2Content & local.subPanelid2Content >
														<cfset local.panelid3Content = local.panelid3Content & local.subPanelid3Content >
														#local.panelid1Content#
														<cfif trim(local.panelid2Content) NEQ ''>
															#local.panelid2Content#
														</cfif>
														<cfif trim(local.panelid3Content) NEQ ''>
															#local.panelid3Content#
														</cfif>
													
												</cfsavecontent>
												
											</cfcase>
											<cfcase value="A">
												<cfsavecontent variable="local.panelListContent">
													<cfloop query="local.thisMember.qryMemberPanels">
														<cfif len(trim(local.thisMember.qryMemberPanels.panelParentID))>&nbsp;&nbsp;&nbsp;&nbsp;</cfif>- #local.thisMember.qryMemberPanels.name#<br/>
													</cfloop>
													<br/>
												</cfsavecontent>
											</cfcase>
										</cfswitch>
										<td style="#local.tdStyle# border-left-style:dotted;border-left-color:##ccc;" width="60%" valign="top">
											<cfif arguments.emailPanelList NEQ 'N'>
												<b style="font-size:95%;">Fields of Practice:</b><br/>	
												#local.panelListContent#
											</cfif>
											<cfif structKeyExists(local.thisMember,"classifications") and isArray(local.thisMember.classifications) and not arrayIsEmpty(local.thisMember.classifications)>
												<cfloop  from="1" to="#arrayLen(local.thisMember.classifications)#" index="local.currentClass">				
													<cfset local.qryClass = local.thisMember.classifications[local.currentClass].qryClass />	
													<cfset local.name = local.thisMember.classifications[local.currentClass].name />
													<cfif local.qryClass.recordCount and local.qryClass.showInSearchResults is 1>					
														<b style="font-size:95%;">#replace(local.name,'_',' ','ALL')#</b><br/>
														<cfloop query="local.qryClass">
															- #local.qryClass.groupName#<br/>
														</cfloop>
														<br/>
													</cfif>
												</cfloop>
											</cfif>	
										</td>
									</cfif>	
								</tr>						
								</table>
							</cfif>
						</cfloop>
						<cfif val(arguments.newsLetterLink)>
						<p style="font-size:13px;color:##333333;">Would you like to receive Newsletters? <a href="#arguments.clientSubscriptionExternalLink#&add=1&cid=#arguments.clientID#">Click here</a> to sign up.</p>
						</cfif>
						<div style="font-size:13px;color:##333333;">#arguments.emailBottomText#</div>						
					</div>																
					<div style="clear:both"></div>
				</body>
			</html>
		</cfoutput>
		</cfsavecontent>
		<cfreturn local.clientEmailContent>
	</cffunction>
	
	<cffunction name="prepareAttorneyEmailContent" access="private" output="false" returntype="string">
		<cfargument name="attroneyName" required="yes" default="" >
		<cfargument name="panelId" required="yes" default="0" >
		<cfargument name="emailTopText" required="yes" default="" >
		<cfargument name="clientFirstName" required="yes" default="" >
		<cfargument name="clientLastName" required="yes" default="" >
		<cfargument name="clientEmail" required="yes" default="" >
		<cfargument name="clientHomePhone" required="yes" default="" >
		<cfargument name="clientCellPhone" required="yes" default="" >
		<cfargument name="clientAlternatePhone" required="yes" default="" >
		<cfargument name="representativeId" required="yes" default="0" >
		<cfargument name="repFirstName" required="yes" default="" >
		<cfargument name="repLastName" required="yes" default="" >
		<cfargument name="repEmail" required="yes" default="" >
		<cfargument name="repHomePhone" required="yes" default="" >
		<cfargument name="repCellPhone" required="yes" default="" >
		<cfargument name="repAlternatePhone" required="yes" default="" >
		<cfargument name="interviewerName" required="yes" default="" >
		<cfargument name="clientReferralID" required="yes" default="0" >
		<cfargument name="referralDate" required="yes" default="" >
		<cfargument name="regTimeZoneName" required="yes" default="" >
		<cfargument name="refIssueDesc" required="yes" default="" >
		<cfargument name="emailBottomText" required="yes" default="" >
		<cfargument name="memberLocalLink" required="yes" default="" >
		<cfargument name="memberExternalLink" required="yes" default="" >
		<cfargument name="siteName" required="yes" default="" >
				
		<cfset var local = structNew()>
		
		<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		
		<cfsavecontent variable="local.emailContent">
			<cfoutput>
				<html>
					<head>
						<title>#arguments.siteName# Referral Confirmation</title>
					</head>
						<body>
						<div style="#local.pageStyle#">																			
							<p style="font-size:13px;color:##333333;">Dear #arguments.attorneyName#:</p> 
							<p style="font-size:13px;color:##333333;">The following client was referred to you on the panel of #arguments.panelid#:</p> 
							<cfif len(trim(arguments.emailTopText))>
								<br>
								<div style="font-size:13px;color:##333333;">#arguments.emailTopText#</div>
							</cfif>							 
							<p style="font-size:13px;color:##333333;">Client referred:</p>
							<table cellpadding="4" cellspacing="0" width="50%" border="1">
							<tr> 
								<td width="25%" style="#local.tdStyle#">Client First Name:</td> 
								<td style="#local.tdStyle#">#arguments.clientFirstName#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Last Name:</td> 
								<td style="#local.tdStyle#">#arguments.clientLastName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Business:</td> 
								<td style="#local.tdStyle#">#arguments.clientBusiness#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Language:</td> 
								<td style="#local.tdStyle#">#arguments.clientLanguage#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client E-mail:</td>
								<td style="#local.tdStyle#">#arguments.clientEmail#</td> 
							</tr> 	
							<tr> 
								<td style="#local.tdStyle#">Client Home Phone ##:</td>
								<td style="#local.tdStyle#">#arguments.clientHomePhone#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Cell Phone ##:</td>
								<td style="#local.tdStyle#">#arguments.clientCellPhone#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Alternate Phone ##:</td>
								<td style="#local.tdStyle#">#arguments.clientAlternatePhone#</td> 
							</tr> 														
							<cfif val(arguments.representativeId)>
								<tr style="#local.tdStyle#"> 
									<td width="25%" style="#local.tdStyle#">Representative First Name:</td> 
									<td style="#local.tdStyle#">#arguments.repFirstName#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Last Name:</td> 
									<td style="#local.tdStyle#">#arguments.repLastName#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative E-mail:</td>
									<td style="#local.tdStyle#">#arguments.repEmail#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative Home Phone ##:</td>
									<td style="#local.tdStyle#">#arguments.repHomePhone#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Cell Phone ##:</td>
									<td style="#local.tdStyle#">#arguments.repCellPhone#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Alternate Phone ##:</td>
									<td style="#local.tdStyle#">#arguments.repAlternatePhone#</td> 
								</tr> 
							</cfif>	
							<tr> 
								<td style="#local.tdStyle#">Interviewer:</td>
								<td style="#local.tdStyle#">#arguments.interviewerName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Referral ##:</td>
								<td style="#local.tdStyle#"><a href="#arguments.memberExternalLink#&ra=editReferral&clientReferralID=#arguments.clientReferralID#">#arguments.clientReferralID#</a></td> 
							</tr>
							</tr>
							<tr>
								<td style="#local.tdStyle#">Date of referral:</td>
								<td style="#local.tdStyle#">#dateFormat(arguments.referralDate,"mm/dd/yyyy")# #timeFormat(arguments.referralDate,"h:mm tt")# #arguments.regTimeZoneName#</td>
							</tr>
							<cfif len(arguments.questionAnswerPath)>
							<tr>
								<td style="#local.tdStyle#">Question/Answer Path:</td>
								<td style="#local.tdStyle#">#arguments.questionAnswerPath#</td> 
							</tr>
							</cfif>
							<tr>
								<td style="#local.tdStyle#">Legal Issue:</td>
								<td style="#local.tdStyle#">#arguments.refIssueDesc#</td> 
							</tr>
							</table>
							<br>
							<div style="font-size:13px;color:##333333;">#arguments.emailBottomText#</div>
						</div>
						<div style="clear:both"></div>
					</body>
				</html>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.emailContent>
	</cffunction>
	
	<cffunction name="showResendReferralEmail" access="public" output="false" returntype="struct" hint="Show resend referral email form">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.pageTitle = "Re-Send E-mails";
			local.clientId = val(arguments.event.getValue('clientID',0));
			local.callUID = arguments.event.getValue('callUID','');
			local.clientreferralid = arguments.event.getValue('clientreferralid',0);
			local.clientEmail = arguments.event.getValue('clientEmail',0);
			local.agencyID = arguments.event.getValue('agencyID',0);
			local.formLink = this.link.resendReferralEmail;
			if(val(local.agencyID))
				local.formLink = this.link.resendAgencyInfoEmail & "&callUID=" & local.callUID;	
			
			local.qryGetAttorneysReferred = this.objAdminReferrals.getAttorneysReferredByCallId(local.callUID);
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_resendReferralEmail.cfm" />			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="resendReferralEmail" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false, "msg": "" }>
		<cfset local.qryGetClientData = this.objAdminReferrals.getClient(clientReferralID=arguments.event.getValue('clientReferralID'))>
		<cfset local.callUID = local.qryGetClientData.callUID>

		<cfif local.qryGetClientData.isCoordination EQ 1>
			<cfset local.data.success = sendReferralCoordinationEmails(event=arguments.event, callUID=local.callUID)>
		<cfelse>
			<cfset local.data.success = doResendReferralEmail(event=arguments.event, callUID=local.callUID)>
		</cfif>

		<cfif local.data.success>
			<cfset local.data.msg = "Referral emails have been sent to selected user(s).">
		<cfelse>
			<cfset local.data.msg = "We were unable to resend the referral emails to selected user(s).">
		</cfif>
		
		<cfreturn returnAppStruct(serializeJson(local.data),"echo")>
	</cffunction>
	
	<cffunction name="doResendReferralEmail" access="private" output="false" returntype="boolean" hint="I send referral email again to selected people">
		<cfargument name="Event" type="any">
		<cfargument name="callUID" type="string" required="true">

		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
			local.objAppBaseLink = CreateObject('component', 'model.apploader');
			local.qryReferralSettings = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
			local.clientMailTopTxt = local.qryReferralSettings.clientMailTopTxt;
			local.clientMailBottomTxt = local.qryReferralSettings.clientMailBottomTxt;
			local.memberMailTopTxt = local.qryReferralSettings.memberMailTopTxt;
			local.memberMailBottomTxt = local.qryReferralSettings.memberMailBottomTxt;
			local.dspEmailPanelList = local.qryReferralSettings.dspEmailPanelList;
			local.dspNewsletterLink = local.qryReferralSettings.dspNewsletterLink;
			local.callUID = arguments.callUID;
			
			local.qryGetCurrentReferralData = this.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),callUID=local.callUID);
			
			local.qryGetReferralData = this.objAdminReferrals.getReferralDataByAttorneyMemberId(orgID=arguments.event.getValue('mc_siteInfo.orgID'),callUID=local.callUID,attorneyMemberID=arguments.event.getValue('attorneyMemberIds',''),allAttorneys=arguments.event.getValue('sendToAllAttorneys',false));
			
			local.thisClientID = val(local.qryGetCurrentReferralData.clientParentID);
			if (not local.thisClientID)
				local.thisClientID = val(local.qryGetCurrentReferralData.clientID);	
			
			local.questionAnswerPath = this.objAdminReferrals.getSearchXMLByClientID(local.thisClientID);
			local.qryGetReferralFilterData = this.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
			local.qryFieldsetID = this.objAdminReferrals.getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='referralsearch');
			local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
			local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
			local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=this.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID);
			local.memberLocalLink = "/?" & local.appBaseLink;
			local.memberExternalLink = "http://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
			local.clientSubscriptionLocalLink = "/?pg=clientRefSubscribe";
			local.clientSubscriptionExternalLink = "http://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.clientSubscriptionLocalLink;
			local.sentToClient = false;
  			// convert times from central (how stored in db) to default timezone of site
  			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
  			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
 			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));		
 			local.referralDate = now();
 			if (local.regTimeZone neq "US/Central") {
 				local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
 			}		
		</cfscript>		

		<cfset local.objReferrals = CreateObject("component","model.referrals.referrals")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.thisPostTypeFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteInfo.siteID'), resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.arrThisPostTypeFields = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren>

		<cfset local.arrClientRefCustomFields = local.objReferrals.getClientReferralCustomFieldWithData(itemID=local.qryGetCurrentReferralData.clientReferralID, itemType='ClientRefCustom', arrResourceFields=local.arrThisPostTypeFields, objCustomFields=local.objResourceCustomFields)>
		<cfset local.panelid1 = "" />
		<cfset local.subpanelid1 = "" />
		<cfset local.radius_dist = "" />
		
		<cfif len(this.emailRecipient)>
			<cfset local.emailAll = replace(this.emailRecipient,";",",","all")>
			<cfset local.sendFrom = trim(listFirst(this.emailRecipient,";"))>
		<cfelse>
			<cfset local.emailAll = "">
			<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
		</cfif>
				
		<cfif arguments.event.getValue('sendToAllAttorneys',false) OR Len(arguments.event.getValue('attorneyMemberIds',''))> 
			<cfloop query="local.qryGetReferralData"><!---looping selected attorneys--->
				<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID) />
				<cfset local.memberName = local.qryMember.firstName & " "	& local.qryMember.lastName>
				<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
				<cfset local.qryGetMemberRefEmails = this.objAdminReferrals.getMemberRefEmails(memberid=local.qryMember.memberID, referralID=this.referralID) />
				<cfset local.memberEmailList = valueList(local.qryGetMemberRefEmails.email) />

				<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
				<cfset local.mailCollectionReplyTo = local.sendFrom>
				<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Client Referral">

				<cfloop query="local.qryGetReferralFilterData">
					<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
						<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
							<cfcase value="panelid1">
								<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
								<cfset local.panelid1 = local.qryGetPanelInfo.name />
							</cfcase>
							<cfcase value="subpanelid1">
								<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
									<cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
									<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
								</cfloop>
							</cfcase>
						</cfswitch>
					</cfif>
					<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
						<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
					</cfif>
				</cfloop>
				<cfif listLen(local.memberEmailList)>
					<cfif local.qryReferralSettings.memberEmailTemplateId neq '' && local.qryReferralSettings.memberEmailTemplateId gt 0>
					
						<cfset local.memberEmailTemplateId = local.qryReferralSettings.memberEmailTemplateId>
						
						<cfset local.objTemplate = this.objAdminReferrals.getReferralEmailTemplate(local.memberEmailTemplateId)>

						<cfset local.referraldata = { 
							referralID=local.qryReferralSettings.referralID
							,siteID = arguments.event.getValue('mc_siteinfo.siteID')
							,sitename = arguments.event.getValue('mc_siteinfo.sitename')
							,orgId = arguments.event.getValue('mc_siteinfo.orgId')
							,orgcode = arguments.event.getValue('mc_siteinfo.orgcode')
							,clientID = local.qryGetReferralData.clientID
							,callUID = local.callUID
							,clientFirstName = local.qryGetReferralData.firstName
							,clientLastName = local.qryGetReferralData.lastName
							,clientFullName = local.qryGetReferralData.firstName &' '&local.qryGetReferralData.middleName &' '&local.qryGetReferralData.lastName
							,memberFirstName = local.qryGetReferralData.memFirstName
							,memberLastName = local.qryGetReferralData.memLastname
							,memberFullName = local.qryGetReferralData.memFirstName &' '&local.qryGetReferralData.memMiddleName &' '&local.qryGetReferralData.memLastname
							,memberCompany = local.qryGetReferralData.company
							,memberMemberNumber = local.qryGetReferralData.memberNumber
							,primaryPanelName = local.panelid1
							,clientEmail = local.qryGetReferralData.email
							,clientHomePhone= local.qryGetReferralData.homePhone
							,clientCellPhone= local.qryGetReferralData.cellPhone
							,clientAlternatePhone= local.qryGetReferralData.alternatePhone
							,clientBusiness = local.qryGetReferralData.businessName
							,clientAddress1 = local.qryGetReferralData.address1
							,clientAddress2 = local.qryGetReferralData.address2
							,clientCity = local.qryGetReferralData.city
							,clientState = local.qryGetReferralData.clientState
							,clientZipCode = local.qryGetReferralData.postalCode
							,clientLanguage = local.qryGetReferralData.languageName
							,representativeId= local.qryGetReferralData.repID
							,repFirstName= local.qryGetReferralData.repFirstName
							,repLastName= local.qryGetReferralData.repLastName
							,repEmail= local.qryGetReferralData.repEmail
							,repHomePhone= local.qryGetReferralData.repHomePhone
							,repCellPhone= local.qryGetReferralData.repCellPhone
							,repAlternatePhone= local.qryGetReferralData.repAlternatePhone
							,interviewerName= local.qryGetReferralData.interviewerName
							,clientReferralID= local.qryGetReferralData.clientReferralID
							,referralDate= local.qryGetReferralData.clientReferralDate
							,regTimeZoneName= local.regTimeZoneName
							,refIssueDesc= local.qryGetReferralData.issueDesc
							,memberExternalLink= local.memberExternalLink
							,transactionDate = ''
							,questionAnswerPath = local.questionAnswerPath
							,showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType') 
							,defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType') 
						}>
						<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
							<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
								<cfoutput>
								<cfif structKeyExists(local.thisField, "value")>
									<cfif isArray(local.thisField.value)>
										<cfloop array="#local.thisField.value#" index="local.thisItem">
											#local.thisItem#
										</cfloop>
									<cfelse>
										#local.thisField.value#
									</cfif>
								</cfif>
							</cfoutput>
							</cfsavecontent>
						</cfloop>
						
						<cfset local.strMemberReferralGrid = this.objAdminReferrals.prepareMemberReferralGrid(referraldata = local.referraldata)>
						<cfset StructInsert(local.referraldata, 'strMemberReferralGrid', local.strMemberReferralGrid,true)>
						<cfset StructInsert(local.referraldata, 'strClientReferralGrid','',true)>
						<cfset StructInsert(local.referraldata, 'strClientPaymentTable','',true)>
												
						<cfset local.strArgs = { content=local.objTemplate.rawContent, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
						
						<cfset local.attroneyEmailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
						<cfset local.fromName = local.objTemplate.emailFromName>
						<cfset local.fromEmail = local.objTemplate.emailFrom>
						<cfset local.subject = local.objTemplate.subjectLine>
						<cfset local.strArgs = { content=local.subject, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
						<cfset local.subject = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
						<cfset local.replyToEmail = local.objTemplate.emailFrom>
					<cfelse>
						<cfset local.attroneyEmailContent = prepareAttorneyEmailContent(
						attorneyName = local.qryGetReferralData.memberName,
						panelId = local.panelid1,
						emailTopText = local.memberMailTopTxt,
						clientFirstName = local.qryGetReferralData.firstName,
						clientLastName = local.qryGetReferralData.lastName,
						clientBusiness = local.qryGetReferralData.businessName,
						clientLanguage = local.qryGetReferralData.languageName,
						clientEmail = local.qryGetReferralData.email,
						clientHomePhone= local.qryGetReferralData.homePhone,
						clientCellPhone= local.qryGetReferralData.cellPhone,
						clientAlternatePhone= local.qryGetReferralData.alternatePhone,representativeId= local.qryGetReferralData.repID,
						repFirstName= local.qryGetReferralData.repFirstName,
						repLastName= local.qryGetReferralData.repLastName,
						repEmail= local.qryGetReferralData.repEmail,
						repHomePhone= local.qryGetReferralData.repHomePhone,
						repCellPhone= local.qryGetReferralData.repCellPhone,
						repAlternatePhone= local.qryGetReferralData.repAlternatePhone,interviewerName= local.qryGetReferralData.interviewerName,clientReferralID= local.qryGetReferralData.clientReferralID,
						referralDate= local.qryGetReferralData.clientReferralDate,
						regTimeZoneName= local.regTimeZoneName,
						refIssueDesc= local.qryGetReferralData.issueDesc,
						emailBottomText= local.memberMailBottomTxt,
						memberLocalLink= local.memberLocalLink,
						memberExternalLink= local.memberExternalLink,
						siteName = arguments.event.getValue('mc_siteinfo.sitename'),questionAnswerPath = local.questionAnswerPath)>
						
						<cfset local.fromName = local.qryReferralSettings.title>
						<cfset local.fromEmail = local.mailCollectionFrom>
						<cfset local.subject = local.mailCollectionSubject>
						<cfset local.replyToEmail = local.mailCollectionReplyTo>
					</cfif>
					
					<cftry>							
						<cfset local.sednEmailStr = this.objAdminReferrals.sendReferralEmail(
							referralID=local.qryReferralSettings.referralID,
							siteid=arguments.event.getValue('mc_siteinfo.siteid'),
							recordedByMemberID=local.qryGetReferralData.enteredByMemberID,
							messageContent=local.attroneyEmailContent,
							contentTitle="Referral Email",
							fromName=local.fromName,
							fromEmail=local.fromEmail,
							replyToEmail=local.replyToEmail,
							subject=local.subject,
							refMemberID=local.qryGetReferralData.memberID,
							refMemberName=local.memberName,
							refMemberEmail=this.emailRecipient,
							messageTypeCode="REFERRALMEMBER")>
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
				</cfif>
			</cfloop>
		</cfif>

		<!---referral email to client--->
		<cfif ((len(trim(local.qryGetCurrentReferralData.email)) and isValid("regex",local.qryGetCurrentReferralData.email,application.regEx.email)) 
			OR (len(trim(local.qryGetCurrentReferralData.repEmail))) and isValid("regex",local.qryGetCurrentReferralData.repEmail,application.regEx.email)) and arguments.event.getValue('sendToClient',false)>			
			<cfscript>					
				local.toEmailList = "";
				if (len(trim(local.qryGetCurrentReferralData.email)) and isValid("regex",local.qryGetCurrentReferralData.email,application.regEx.email))
					local.toEmailList = local.qryGetCurrentReferralData.email;
	
				if (len(trim(local.qryGetCurrentReferralData.repEmail)) and isValid("regex",local.qryGetCurrentReferralData.repEmail,application.regEx.email))
					local.toEmailList = iif(len(trim(local.toEmailList)),de("#local.toEmailList#,"),de("")) & local.qryGetCurrentReferralData.repEmail;
	
				if (len(trim(local.emailAll)) and isValid("regex",local.emailAll,application.regEx.email))
					local.toEmailList = ListAppend(local.toEmailList, local.emailAll);		
			</cfscript>

			<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
			<cfset local.mailCollectionReplyTo = local.sendFrom>
			<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Attorney Referral">				
			<cfif local.qryReferralSettings.clientEmailTemplateId neq '' && local.qryReferralSettings.clientEmailTemplateId gt 0>
					
				<cfset local.clientEmailTemplateId = local.qryReferralSettings.clientEmailTemplateId>
				
				<cfset local.objTemplate = this.objAdminReferrals.getReferralEmailTemplate(local.clientEmailTemplateId)>
				<cfset local.referraldata = { 
					referralID=local.qryReferralSettings.referralID
					,siteID = arguments.event.getValue('mc_siteinfo.siteID')
					,sitename = arguments.event.getValue('mc_siteinfo.sitename')
					,orgId = arguments.event.getValue('mc_siteinfo.orgId')
					,orgcode = arguments.event.getValue('mc_siteinfo.orgcode')
					,clientID = local.qryGetReferralData.clientID
					,callUID = local.callUID
					,clientFirstName = local.qryGetReferralData.firstName
					,clientLastName = local.qryGetReferralData.lastName
					,clientFullName = local.qryGetReferralData.firstName &' '&local.qryGetReferralData.middleName &' '&local.qryGetReferralData.lastName
					,memberFirstName = local.qryGetReferralData.memFirstName
					,memberLastName = local.qryGetReferralData.memLastname
					,memberFullName = local.qryGetReferralData.memFirstName &' '&local.qryGetReferralData.memMiddleName &' '&local.qryGetReferralData.memLastname
					,memberCompany = local.qryGetReferralData.company
					,memberMemberNumber = local.qryGetReferralData.memberNumber
					,primaryPanelName = local.panelid1
					,clientEmail = local.qryGetReferralData.email
					,clientHomePhone= local.qryGetReferralData.homePhone
					,clientCellPhone= local.qryGetReferralData.cellPhone
					,clientAlternatePhone= local.qryGetReferralData.alternatePhone
					,clientBusiness = local.qryGetReferralData.businessName
					,clientAddress1 = local.qryGetReferralData.address1
					,clientAddress2 = local.qryGetReferralData.address2
					,clientCity = local.qryGetReferralData.city
					,clientState = local.qryGetReferralData.clientState
					,clientZipCode = local.qryGetReferralData.postalCode
					,representativeId= local.qryGetReferralData.repID
					,repFirstName= local.qryGetReferralData.repFirstName
					,repLastName= local.qryGetReferralData.repLastName
					,repEmail= local.qryGetReferralData.repEmail
					,repHomePhone= local.qryGetReferralData.repHomePhone
					,repCellPhone= local.qryGetReferralData.repCellPhone
					,repAlternatePhone= local.qryGetReferralData.repAlternatePhone
					,interviewerName= local.qryGetReferralData.interviewerName
					,clientReferralID= local.qryGetReferralData.clientReferralID
					,referralDate= local.referralDate
					,regTimeZoneName= local.regTimeZoneName
					,transactionDate = ''
					,refIssueDesc= local.qryGetReferralData.issueDesc
					,memberExternalLink= local.memberExternalLink
					,questionAnswerPath = local.questionAnswerPath
					,showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType') 
					,defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType') 
				}>
				<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
					<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
						<cfoutput>
						<cfif structKeyExists(local.thisField, "value")>
							<cfif isArray(local.thisField.value)>
								<cfloop array="#local.thisField.value#" index="local.thisItem">
									#local.thisItem#
								</cfloop>
							<cfelse>
								#local.thisField.value#
							</cfif>
						</cfif>
					</cfoutput>
					</cfsavecontent>
				</cfloop>
				<cfset local.strClientReferralGrid = this.objAdminReferrals.prepareClientReferralGrid(referraldata = local.referraldata)>
				
				<cfset StructInsert(local.referraldata, 'strClientReferralGrid', local.strClientReferralGrid,true)>
				<cfset StructInsert(local.referraldata, 'strMemberReferralGrid','',true)>
				<cfset StructInsert(local.referraldata, 'strClientPaymentTable','',true)>
				
				
				<cfset local.strArgs = { content=local.objTemplate.rawContent, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
				
				<cfset local.clientEmailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>

				<cfset local.fromName = local.objTemplate.emailFromName>
				<cfset local.fromEmail = local.objTemplate.emailFrom>
				<cfset local.subject = local.objTemplate.subjectLine>
				<cfset local.strArgs = { content=local.subject, referraldata=local.referraldata, orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.siteCode')}>
					
				<cfset local.subject = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
				<cfset local.replyToEmail = local.objTemplate.emailFrom>
			<cfelse>
				<cfset local.clientEmailContent = prepareClientReferralEmailContent(
				sitename=arguments.event.getValue('mc_siteinfo.sitename')
				,emailTopText=local.clientMailTopTxt
				,emailBottomText=local.clientMailBottomTxt
				,orgId=arguments.event.getValue('mc_siteInfo.orgID')
				,orgcode=arguments.event.getValue('mc_siteInfo.orgcode')
				,emailPanelList=local.dspEmailPanelList
				,clientId=local.thisClientID
				,callUID=local.callUID
				,newsLetterLink=local.dspNewsletterLink
				,clientSubscriptionExternalLink = local.clientSubscriptionExternalLink)>
				
				<cfset local.fromName = local.qryReferralSettings.title>
				<cfset local.fromEmail = local.mailCollectionFrom>
				<cfset local.subject = local.mailCollectionSubject>
				<cfset local.replyToEmail = local.mailCollectionReplyTo>
			</cfif>	
			
			<cftry>				
				<cfset local.sednEmailStr = this.objAdminReferrals.sendReferralEmail(
				referralID=local.qryReferralSettings.referralID,
				siteid=arguments.event.getValue('mc_siteinfo.siteid'),
				recordedByMemberID=local.qryGetReferralData.enteredByMemberID,
				messageContent=local.clientEmailContent,
				contentTitle="Referral Email",
				fromName=local.fromName,
				fromEmail=local.fromEmail,
				replyToEmail=local.replyToEmail,
				subject=local.subject,
				refMemberID=local.qryReferralSettings.clientFeeMemberID,
				refMemberName=local.qryGetReferralData.clientName,
				refMemberEmail=local.toEmailList,
				messageTypeCode="REFERRALCLIENT")>	

			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn true>	
	</cffunction>
	
	<cffunction name="editStatus" access="public" output="false" returntype="struct" hint="edit status">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.referralID = this.referralID;
			local.statusid = arguments.event.getValue('statusid',0);
			local.importcode=arguments.event.getTrimValue('importCode','');
			local.qryGetStatus = this.objAdminReferrals.getStatusByID(statusID=local.statusID);
			local.primaryStatus = local.qryGetStatus.primaryStatus;
			local.statusName = local.qryGetStatus.statusName;
			local.statusCode = local.qryGetStatus.statusCode;
			local.dspFrontEnd = local.qryGetStatus.dspFrontEnd;
			local.isRetainedcase = local.qryGetStatus.isRetainedcase;
			local.isClosedByLawyer = local.qryGetStatus.isClosedByLawyer;
			local.isReferred = local.qryGetStatus.isReferred;
			local.isActive =  local.qryGetStatus.isActive;
			local.isConsultation = local.qryGetStatus.isConsultation;

			local.saveFormLink = buildCurrentLink(arguments.event,"saveStatus")  & "&mode=stream";
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_status.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="saveStatus" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			if (arguments.event.getValue('statusID'))
				this.objAdminReferrals.updateStatus(arguments.event);
			else
				var statusID = this.objAdminReferrals.insertStatus(arguments.event);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshStatusGrid();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="transferReferralPanel" access="public" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />
		<cfset local.returnStruct = StructNew() />
		<cfset local.returnStruct.success = true />
		<cfset local.clientReferralID = arguments.event.getValue('clientReferralID',0) />
		<cfset local.clientID = arguments.event.getValue('clientID',0) />
		<cfset local.panelID = arguments.event.getValue('panelid1',0) />
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID') />
		<cfset local.thisClientID = arguments.event.getValue('thisClientID',0) />
		<cfset local.prevPanelID =  arguments.event.getValue('prevPanelID',0)>

		<cfif val(local.prevPanelID) and local.panelID neq local.prevPanelID>
			<cfset local.qryGetPaymentData = this.objAdminReferrals.getClientPaymentData(clientID=local.thisClientID) />
			<cfset local.clientID = local.thisClientID />
			<cfset local.qryGetPrimPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.panelID) />
			<cfset arguments.event.setValue('clientReferralAmount',val(local.qryGetPrimPanelInfo.clientreferralamount)) />
			<cfset local.clientreferralamount = val(local.qryGetPrimPanelInfo.clientreferralamount) />
			<cfset local.panelName = local.qryGetPrimPanelInfo.name />
			<cfset local.qryClientPaymentData = this.objAdminReferrals.getClientPaymentData(clientID=local.thisClientID) />
			<cfset local.stateIDForTax = local.qryClientPaymentData.stateIDForTax />
			<cfset local.zipForTax = local.qryClientPaymentData.zipForTax />
			<cfset local.clientFeeMemberID = this.clientFeeMemberID />			
			<cfset local.GLAccountID = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).clientGLAccountID />
			<cfset local.clientName = arguments.event.getValue('firstName') & " " & arguments.event.getValue('lastName') />
			<cfset local.callUID = arguments.event.getValue('callUID') />
			<cfset local.GLAccountID = iif(val(local.qryGetPrimPanelInfo.clientFeeGLAccountID), local.qryGetPrimPanelInfo.clientFeeGLAccountID , local.GLAccountID) />
			<cfset local.totalTaxAmt = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(
				saleGLAccountID=val(local.GLAccountID),
				saleAmount=local.clientreferralamount, 
				transactionDate="#dateformat(now(),"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
				stateIDForTax=val(local.stateIDForTax), 
				zipForTax=local.zipForTax).totalTaxAmt />
			<cfset local.resultStr = this.objAdminReferrals.adjustPanelClientPayment(acctStr=local) />
		</cfif> 

		<cfreturn local.returnStruct/>		
	</cffunction>
	
	<cffunction name="transferCaseMemberUpdate" access="public" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>		
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.clientReferralID = arguments.event.getValue('clientReferralID',0)/>
		<cfset local.caseID = val(arguments.event.getValue('caseID',0))/>		
		<cfset local.prevMemberID = val(arguments.event.getValue('memberID',0))/>
		<cfset local.transferMemberId = val(arguments.event.getValue('transferMemberId',0))/>		
		<cfset local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets")/>
		<cfset local.qryPrevMember = application.objMember.getMemberInfo(local.prevMemberID) />	
		<cfset local.qryTransferMember = application.objMember.getMemberInfo(local.transferMemberId) />		
		<cfset local.qryGetTransferMemberRefEmails = this.objAdminReferrals.getMemberRefEmails(memberid=local.qryTransferMember.memberID, referralID=this.referralID) />
		<cfset local.memberTransferEmail = valueList(local.qryGetTransferMemberRefEmails.email) />
		<cfset local.emailRecipient = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).emailRecipient/>
		<cfset local.prevMemberNameString = local.qryPrevMember.firstName & " "	& local.qryPrevMember.lastName>
		<cfset local.transferMemberNameString = local.qryTransferMember.firstName & " "	& local.qryTransferMember.lastName & " (" & local.qryTransferMember.memberNumber & ")">
		<cfset local.qryReferralSettings = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')) />
		<cfset local.title = local.qryReferralSettings.title />

		<cfif local.clientReferralID GT 0>
			<cftry>
				<cfquery name="local.qryTransferCase" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @referralID int = <cfqueryparam value="#this.referralID#" cfsqltype="cf_sql_integer">;

						BEGIN TRAN;

							UPDATE dbo.ref_clientReferrals
							SET memberID = <cfqueryparam value="#local.transferMemberId#" cfsqltype="cf_sql_integer"/>
							WHERE referralID = @referralID
							AND clientReferralID = <cfqueryparam value="#local.clientReferralID#" cfsqltype="cf_sql_integer"/>;

							<cfif local.caseID GT 0>
								UPDATE dbo.ref_cases
								SET enteredByMemberID = <cfqueryparam value="#local.transferMemberId#" cfsqltype="cf_sql_integer"/>
								WHERE caseID = <cfqueryparam value="#local.caseID#" cfsqltype="cf_sql_integer"/>
								AND clientReferralID = <cfqueryparam value="#local.clientReferralID#" cfsqltype="cf_sql_integer"/>;
							</cfif>

							INSERT INTO dbo.ref_transferHistory (prevMemberID, referralID, clientReferralID, transferDt)
							VALUES (
								<cfqueryparam value="#local.prevMemberID#" cfsqltype="cf_sql_integer" />,
								@referralID,
								<cfqueryparam value="#local.clientReferralID#" cfsqltype="cf_sql_integer" />,
								<cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
							);

							SELECT scope_identity() as transferHistoryID;

						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfif val(local.qryTransferCase.transferHistoryID)>
					<cfset local.thisChange =  [{ITEM="Attorney",OLDVALUE=local.prevMemberNameString ,NEWVALUE=local.transferMemberNameString}]>
					<cfset createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteInfo.orgID')),
						clientReferralID=int(local.clientReferralID), actorMemberID=int(session.cfcuser.memberdata.memberid), mainMessage="Referral Transferred", changes=local.thisChange)>			
				</cfif>

				<cfcatch type="Any">
					<cfset local.returnStruct.success = false>
					<cfset application.objError.sendError(cfcatch=cfcatch)>
				</cfcatch>
			</cftry>	
		</cfif>

		<cfif listLen(local.memberTransferEmail) and local.qryTransferCase.transferHistoryID GT 0>

			<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
			<cfset local.mailCollectionReplyTo = '<EMAIL>'>
			<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteinfo.orgname')# Referral Transfer">			

			<cfoutput>					
				<cfsavecontent variable="local.emailContent">
					<html>
						<head>
							<title>#arguments.event.getValue('mc_siteinfo.orgname')# - Referral Transfer</title>
						</head>
						<body>
							<div style="width:100%;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;">	
								<p style="font-size:13px;color:##333333;">
									Dear #local.qryTransferMember.firstname# #local.qryTransferMember.lastname#:							
								</p>
								<p style="font-size:13px;color:##333333;">
									This email is to notify you that you have been transferred case <a href="#arguments.event.getValue('mc_siteinfo.scheme')#://#arguments.event.getValue('mc_siteinfo.mainHostname')#/?pg=referrals&ra=editReferral&clientReferralID=#local.clientReferralID#">Referral #local.clientReferralID# - Client: #arguments.event.getValue('lastName','')#, #arguments.event.getValue('firstName','')#</a><cfif len(trim(local.title))> by the #local.title# staff</cfif>.
								</p>
								
								<p style="font-size:13px;color:##333333;">
									If you have any questions, please contact us.<br/><br/>
									#local.title#
								</p>
							</div>
							<div style="clear:both"></div>
						</body>
					</html>
				</cfsavecontent>

				<cftry>					
					<cfset local.sednEmailStr = this.objAdminReferrals.sendReferralEmail(referralID=this.referralID,
																						siteid=arguments.event.getValue('mc_siteinfo.siteid'),
																						recordedByMemberID=local.transferMemberId,
																						messageContent=local.emailContent,
																						contentTitle="#arguments.event.getValue('mc_siteinfo.orgname')# - Referral Transfer",
																						fromName=local.title,
																						fromEmail=local.mailCollectionFrom,
																						replyToEmail=local.mailCollectionReplyTo,
																						subject=local.mailCollectionSubject,
																						refMemberID=local.transferMemberId,
																						refMemberName=local.transferMemberNameString,
																						refMemberEmail=local.emailRecipient,
																						messageTypeCode="REFERRALMEMBER")>	

					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch) />
					</cfcatch>
				</cftry>
			</cfoutput>
		</cfif>

		<cfreturn local.returnStruct/>		
	</cffunction>
	
	<cffunction name="showTransferCaseMemberList" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	
			local.siteResourceID = this.siteResourceID;
			local.urlString = "";
			local.transferMemberList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=referralsXML&meth=transferMemberXML&mode=stream&referralID=#this.referralID#&siteresId=#local.siteResourceID#";
			
			local.transferMemberList = local.transferMemberList & '&panelid1=#arguments.event.getValue('panelid1','')#' & '&panelid2=#arguments.event.getValue('panelid2','')#' & '&panelid3=#arguments.event.getValue('panelid3','')#' & '&subpanelid1=#arguments.event.getValue('subpanelid1','')#'
			& '&subpanelid2=#arguments.event.getValue('subpanelid2','')#' & '&subpanelid3=#arguments.event.getValue('subpanelid3','')#'  & '&currentMemberId=#arguments.event.getValue('currentMemberId',0)#';
			
			if( arguments.event.getTrimValue('showAllMembers',0) )
				local.transferMemberList = "#local.transferMemberList#&showAllMembers=#arguments.event.getTrimValue('showAllMembers',0)#";
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				 <cfinclude template="frm_transferCaseMemberList.cfm" />		
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageQuestions" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			local.readOnly = 0;
			local.pageTitle = "Manage Questions";
			local.gridwidth = 690;
			local.referralID = this.referralID;
			if(arguments.event.valueExists('panelID') and val(arguments.event.getValue('panelID')) gt 0) {
				local.qryGetPanelByID = this.objAdminReferrals.getPanelByID(panelID=arguments.event.getValue('panelID'));
				local.controllingSRID = local.qryGetPanelByID.siteResourceID;
				local.pageTitle = "Manage Questions for Sub-Panels";
				local.isPanelTab = true;
				
				local.readOnly = arguments.event.getValue('readOnly',0);
				if (local.readOnly is 1) {
					local.pageTitle = "";
					local.gridwidth = 590;
				}
			} else {
				local.controllingSRID = this.siteResourceID;
				local.exportQAStructureZIPLink = buildCurrentLink(arguments.event,"exportQAStructureZIP") & "&mode=stream";
				local.doImportQALink = buildCurrentLink(arguments.event,"doImportQuestionAnswers") & "&mode=stream";
				local.isPanelTab = false;
			}

			// Build breadCrumb Trail
			if(NOT arguments.event.valueExists('panelID')) {
				appendBreadCrumbs(arguments.event,{ link='', text='Manage Questions' });
			}
			local.qryGetParentQuestions = this.objAdminReferrals.getParentQuestions(siteID=arguments.event.getValue('mc_siteInfo.siteID'),controllingSRID=local.controllingSRID);
		</cfscript>
		
		<cfsavecontent variable="local.questionTreeHTML">
			<cfoutput>
				<cfif local.qryGetParentQuestions.recordCount>
					<cfloop query="local.qryGetParentQuestions">
						#processQuestionTreeNode(type="Question",uniqueID=local.qryGetParentQuestions.fieldID,label=local.qryGetParentQuestions.fieldText,sortOrder=local.qryGetParentQuestions.fieldOrder,recordCount=local.qryGetParentQuestions.recordCount)#
					</cfloop>
				<cfelse>
					No Question added
				</cfif>			
			</cfoutput>
		</cfsavecontent>

		<cfif arguments.event.getValue('tab','') eq 'ex'>
			<cfif arguments.event.getValue('importFileName','') neq ''>
				<cfset local.prepResult = prepareQuestionAnswerImport(event=arguments.event)>
			</cfif>
		</cfif>
		
		<cfif local.isPanelTab>
			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_listQuestions.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_manageQuestions.cfm">
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processQuestionTreeNode" access="public" output="false" returnType="string" hint="I prepare the html for Question tree">
		<cfargument name="uniqueID" required="true" type="numeric" default="0">
		<cfargument name="label" required="true" type="string" default="">
		<cfargument name="type" required="true" type="string" default="Question">
		<cfargument name="isPanelTree" required="true" type="boolean" default="false">
		<cfargument name="condition" required="false" type="string" default="">
		<cfargument name="conditionFieldID" required="false" type="string" default="">
		<cfargument name="conditionvalue" required="false" type="string" default="">
		<cfargument name="sortOrder" required="true" type="numeric" default="0">
		<cfargument name="recordCount" required="true" type="numeric" default="0">
		
		<cfset var local = structNew()>
		<cfset local.qryGetAllSubPanels = QueryNew('')>
		<cfset local.qryGetAllSubPanelsForFrontEnd = QueryNew('')>
        <cfset local.objAdminReferrals = CreateObject("component","model.admin.referrals.referrals")>
		<cfset local.qryGetAnswers = QueryNew('')>
		<cfset local.qryGetConditionRelatedQuestions = QueryNew('')>
		<cfset local.qryGetPanelDetailsByValueID = QueryNew('')>
		<cfif arguments.type EQ "Question">
			<cfquery name="local.qryGetAnswers" datasource="#application.dsn.membercentral.dsn#">
				select valueID, valueString, valueOrder from dbo.cf_fieldValues where fieldID = #arguments.uniqueID# order by valueOrder;
			</cfquery>
			
		</cfif>
		<cfif arguments.type EQ "Answer">
			<cfquery name="local.qryGetPanelDetailsByValueID" datasource="#application.dsn.membercentral.dsn#">
				select rp.panelID, rp.name, rp.siteResourceID, feDspClientReferral from ref_panels rp
				INNER JOIN dbo.cf_fieldValueSiteResources fvs on fvs.siteresourceID = rp.siteresourceID
				where valueID = #arguments.uniqueID# 
				--and rp.feDspClientReferral = 1
			</cfquery>
			<cfquery name="local.qryGetFieldIDByValueID" datasource="#application.dsn.membercentral.dsn#">
				select fv.fieldID from dbo.cf_fieldValues fv
				where fv.valueID = #arguments.uniqueID# 
			</cfquery>
			<cfquery name="local.qryGetConditionRelatedQuestions" datasource="#application.dsn.membercentral.dsn#">		
				select f.fieldID,f.fieldText,fvc.condFieldID, fvc.condValueID,fvc.condExp,f.fieldOrder
				from dbo.cf_fieldValueConditions fvc
				inner join cf_fields f on f.fieldid = fvc.fieldid
				where condFieldID = #local.qryGetFieldIDByValueID.fieldID# and fvc.condValueID = #arguments.uniqueID#
				order by fieldOrder
			</cfquery>
		</cfif>
		<cfif arguments.type CONTAINS "Question">
			<cfset local.tagName = "Question">
			<cfset local.buttonBagName = "Answer">
			<cfset local.editFunc  = "editQuestion(#arguments.uniqueID#);">
			<cfset local.removeFunc  = "removeQuestion(#arguments.uniqueID#);">
			<cfset local.arrowImageColor = "text-success">
		<cfelse>
			<cfset local.tagName = "Answer">
			<cfset local.buttonBagName = "Question">
			<cfset local.editFunc  = "editAnswer(#arguments.uniqueID#,#local.qryGetFieldIDByValueID.fieldID#);">
			<cfset local.removeFunc  = "removeAnswer(#arguments.uniqueID#,#local.qryGetFieldIDByValueID.fieldID#,#local.qryGetPanelDetailsByValueID.recordCount#);">
			<cfset local.arrowImageColor = "">
		</cfif>
		<cfsavecontent variable="local.questionTreeHTML">
			<cfoutput>
				<div class="mcExpand">
					<cfif (arguments.type CONTAINS "Answer" AND local.qryGetConditionRelatedQuestions.recordCount) OR (arguments.type CONTAINS "Question" AND local.qryGetAnswers.recordCount)>
						<img src="/assets/common/javascript/dhtmlxgrid/imgs/plus.gif" align="absmiddle" onclick="" title=" Expand #arguments.type#">&nbsp;
						<cfset local.hideSection = true>
					</cfif>
					<h6>
						<span>#arguments.label#</span>
					</h6>
				</div>
				<div class="catRow cat#arguments.type##arguments.uniqueID#" id="#local.tagName#_#arguments.uniqueID#">
					
					<div class="actionMenuWrapper <cfif local.tagName EQ 'Answer'> row</cfif>">
						<div class="actionMenuContainer d-inline-block <cfif local.tagName EQ 'Answer'> col-sm-8 col-xl-6 p-0</cfif>">
							<ul class="actionMenu">
								<li>
									<div>
										<a href="javascript:#local.editFunc#" target="_self" title="Edit #local.tagName#" >
											<i class="fa-regular fa-pen-to-square align-middle" title="Edit #local.tagName#"></i> Edit
										</a>
									</div>
								</li>
								<li>
									<div <cfif local.qryGetAnswers.recordCount NEQ 0 >class="noActionItem"</cfif>>
										<a id="btnDel#arguments.uniqueID#" <cfif local.qryGetAnswers.recordCount EQ 0 >href="javascript:#local.removeFunc#"<cfelse>href="javascript:void(0);"</cfif> target="_self" title="Delete #local.tagName#">
											<i class="fa-regular fa-circle-minus text-danger align-middle" title="Delete #local.tagName#"></i> Remove
										</a>
									</div>
								</li>
								<li>
									<div <cfif arguments.sortOrder EQ 1>class="noActionItem"</cfif>>
										<a <cfif arguments.sortOrder NEQ 1>href="javascript:move#local.tagName#(#arguments.uniqueID#,'up')"<cfelse>href="javascript:void(0);"</cfif> target="_self" title="Move #local.tagName# Up"><i class="fa-solid fa-up #local.arrowImageColor# align-middle" title="Move #local.tagName# Up"></i> Move Up</a>
									</div>
								</li>
								<li>
									<div <cfif NOT(arguments.sortOrder NEQ arguments.recordCount)>class="noActionItem"</cfif>>
										<a <cfif arguments.sortOrder NEQ arguments.recordCount>href="javascript:move#local.tagName#(#arguments.uniqueID#,'down')"<cfelse>href="javascript:void(0);"</cfif> title="Move #local.tagName# Down" target="_self">
											<i class="fa-solid fa-down #local.arrowImageColor# align-middle" title="Move #local.tagName# Down"></i> Move Down
										</a>
									</div>
								</li>
							</ul>
						</div>
						<cfif NOT local.qryGetPanelDetailsByValueID.recordCount>
							<cfif local.tagName NEQ "Answer">
								<div class="actionMenuButton"><button type="button" onclick="addAnswer(#arguments.uniqueID#);"  title="Add #local.buttonBagName#" class="btn btn-secondary btn-sm border border-success align-middle float-right mt-1"><i class="fa-regular fa-circle-plus align-middle"></i> Add #local.buttonBagName#</button></div>
							<cfelse>
								<div class="actionMenuButton col-sm-8 col-xl-6"><button type="button" onclick="addQuestion(#arguments.uniqueID#);"  title="Add #local.buttonBagName#" class="btn btn-secondary btn-sm border border-success align-middle float-right mt-1"><i class="fa-regular fa-circle-plus align-middle"></i> Add #local.buttonBagName#</button></div>
							</cfif>
						<cfelse>
							<div class="actionMenuButton <cfif local.tagName EQ 'Answer'> col-sm-4 col-xl-6</cfif>"><i><b>Panel(s) Assigned</b>: #ValueList(local.qryGetPanelDetailsByValueID.name)#</i></div>
						</cfif>
					</div>
					<cfif local.qryGetAnswers.recordCount>
						<div style="margin-left:20px; position: relative;" class="mc_row" >
							<cfloop query="local.qryGetAnswers">
								#processQuestionTreeNode(isPanelTree=arguments.isPanelTree,type="Answer",uniqueID=local.qryGetAnswers.valueID,label=local.qryGetAnswers.valueString,sortOrder=local.qryGetAnswers.valueOrder,recordCount=local.qryGetAnswers.recordCount)#
							</cfloop>
						</div>
					</cfif>

					<cfif arguments.type eq "Answer">
						<cfif local.qryGetConditionRelatedQuestions.recordCount>
							<div style="margin-left:20px;position: relative;" class="mc_row">
								<cfloop query="local.qryGetConditionRelatedQuestions">
									#processQuestionTreeNode(type="Question",conditionvalue="#local.qryGetConditionRelatedQuestions.condValueID#",condition="#local.qryGetConditionRelatedQuestions.condExp#",conditionFieldid="#local.qryGetConditionRelatedQuestions.condFieldid#",uniqueID=local.qryGetConditionRelatedQuestions.fieldID,label=local.qryGetConditionRelatedQuestions.fieldText, sortOrder=local.qryGetConditionRelatedQuestions.fieldOrder,recordCount=local.qryGetConditionRelatedQuestions.recordCount)#
								</cfloop>
							</div>
						</cfif>
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.questionTreeHTML>
	</cffunction>

	<cffunction name="editQuestion" access="public" output="false" returntype="struct" hint="I return Add Question Page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.errmsg="";
			local.referralID = this.referralID;
			local.panelID = arguments.event.getValue('panelID',0);
			local.csrid = arguments.event.getValue('csrid',this.siteResourceID);
			local.fieldID = arguments.event.getValue('fieldID',0);
			local.fieldText = arguments.event.getValue('fieldText',"");
			local.fieldReference = arguments.event.getValue('fieldReference',"");
			local.fieldUID = arguments.event.getValue('fieldUID',"");
			local.displayType = arguments.event.getValue('displayType',"");
			local.fieldType = arguments.event.getValue('fieldType',"");
			local.parentUsageID = arguments.event.getValue('parentUsageID',0);
			local.formLink = this.link.editQuestion;

			local.valueID = arguments.event.getValue('valueID',0);
			local.parentFieldID = 0;
			local.pageTitle = "Add Question";
		</cfscript>
		<cfquery name="local.qryGetUsage" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			declare @usageID int;
			select  @usageID  = dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser', NULL);

			select usageID, areaName, areaDesc, parentUsageID, offerDisplayOnly, allowBranching
			from dbo.cf_fieldUsages
			where usageID = @usageID;
		</cfquery>
		<cfquery name="local.qryGetDisplayType" datasource="#application.dsn.membercentral.dsn#">
			select fieldTypeCode, displayTypeCode from dbo.cf_fieldTypes where displayTypeCode = 'RADIO' AND fieldTypeCode = 'STRINGRADIO'
		</cfquery>
		<cfset local.qryParentField = QueryNew('')>
		<cfif val(local.valueID)>
			<cfquery name="local.qryParentField" datasource="#application.dsn.membercentral.dsn#">
				select fieldID from dbo.cf_fieldValues where valueID = #local.valueID#
			</cfquery>
		</cfif>
	
		<cfscript>
			local.usageID = local.qryGetUsage.usageID;
			local.parentUsageID = local.qryGetUsage.parentUsageID;
			local.displayType = local.qryGetDisplayType.displayTypeCode;
			local.fieldType = local.qryGetDisplayType.fieldTypeCode;
			local.fieldReference = createUUID();
			local.isRequired = false;
			if(val(local.valueID))
				local.parentFieldID = local.qryParentField.fieldID;
			local.panelID = this.objAdminReferrals.getReferralPanelIDFromSiteResourceID(siteResourceID=local.csrid);
			
			if(val(local.fieldID)){	
				local.qryGetQuestion = this.objResourceCustomFields.getFieldDetails(csrid=local.csrid, fieldID=local.fieldID, usageID=local.usageID);
				
				local.fieldText = local.qryGetQuestion.fieldText;
				local.fieldReference = local.qryGetQuestion.fieldReference;
				local.fieldUID = local.qryGetQuestion.uid;
				local.isRequired = local.qryGetQuestion.isRequired;
				local.pageTitle = "Edit Question";
			}		
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_question.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="editAnswer" access="public" output="false" returntype="struct" hint="I return Add Answer Page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.errmsg="";
			local.referralID = this.referralID;
			local.panelID = arguments.event.getValue('panelID',0);
			local.csrid = arguments.event.getValue('csrid',this.siteResourceID);
			local.fieldID = arguments.event.getValue('fieldID',0);
			local.valueID = arguments.event.getValue('valueID',0);
			local.fieldValue = arguments.event.getValue('fieldValue',"");
			local.parentUsageID = arguments.event.getValue('parentUsageID',0);

			local.formLink = this.link.editAnswer;
			local.pageTitle = "Add Answer";
		</cfscript>
		<cfquery name="local.qryGetUsage" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			declare @usageID int;
			select  @usageID  = dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser', NULL);

			select usageID, areaName, areaDesc, parentUsageID, offerDisplayOnly, allowBranching
			from dbo.cf_fieldUsages
			where usageID = @usageID;
		</cfquery>
	
		<cfscript>
			local.usageID = local.qryGetUsage.usageID;
			local.parentUsageID = local.qryGetUsage.parentUsageID;
			local.qryFieldValueSiteResources = QueryNew('');

			local.panelID = this.objAdminReferrals.getReferralPanelIDFromSiteResourceID(siteResourceID=local.csrid);
			local.qrySiteResources = this.objAdminReferrals.getReferralPanelsForResourceFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), panelID=local.panelID);
			local.qryFieldValueSiteResources = this.objResourceCustomFields.getFieldValueSiteResources(valueID=val(local.valueID));
			if(val(local.valueID)){	
				local.qryFieldValue = this.objResourceCustomFields.getFieldOptions(fieldID=local.fieldID, restrictToValueIDList=local.valueID);
				local.fieldValue = local.qryFieldValue.fieldValue;
				local.isRequired = true;
				local.pageTitle = "Edit Answer";
			}
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_answer.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="getQuestionPanelCount" access="public" output="false" returntype="struct" hint="I return unassigned and assigned panels">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();

			local.panelID = arguments.event.getValue("panelID",0)

			if(arguments.event.valueExists('panelID') and val(arguments.event.getValue('panelID')) gt 0) {
				local.qryGetPanelByID = this.objAdminReferrals.getPanelByID(panelID=arguments.event.getValue('panelID'));
				local.controllingSRID = local.qryGetPanelByID.siteResourceID;
			} else {
				local.controllingSRID = this.siteResourceID;
			}

			local.qryGetQuestionPanelCount = this.objAdminReferrals.getQuestionPanelCount(referralID=this.referralID, controllingSRID=local.controllingSRID, panelID=local.panelID);
		</cfscript>
		
		<cfquery name="local.qryGetQuestionsWithPanel" dbtype="query">
			SELECT panelID, name, shortDesc, panelCount, statusName, feDspClientReferral
			FROM local.qryGetQuestionPanelCount 
			WHERE panelCount > 0
		</cfquery>
		<cfquery name="local.qryGetQuestionsWithoutPanel" dbtype="query">
			SELECT panelID, name, shortDesc, panelCount, statusName, feDspClientReferral 
			FROM local.qryGetQuestionPanelCount 
			WHERE panelCount = 0
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h6>Assigned Panels</h6>
				<cfif local.qryGetQuestionsWithPanel.recordCount>
					<cfloop query="local.qryGetQuestionsWithPanel">
						<div class="row questionPanel pt-3 pb-3 m-1 border-bottom border-secondary">
							<div class="col-10 p-0">
								#local.qryGetQuestionsWithPanel.name# (#local.qryGetQuestionsWithPanel.panelCount#)
							</div>
							<div class="col-2">
								<cfif local.qryGetQuestionsWithPanel.statusName EQ "Active"><i class="fa-solid fa-circle-check text-info" title="#local.qryGetQuestionsWithPanel.statusname#"></i><cfelse><i class="fa-solid fa-circle-check text-grey" title="#local.qryGetQuestionsWithPanel.statusname#"></i></cfif>
								&nbsp;&nbsp;
								<cfif val(local.qryGetQuestionsWithPanel.feDspClientReferral) is 1><i class="fa-regular fa-eye" title="Displays in Front-end"></i><cfelse><i class="fa-regular fa-eye-slash" title="Doesn't display in Front-end"></i></cfif>
							</div>
						</div>
					</cfloop>
				<cfelse>
					<div class="row">
						<div class="col-12">
							No Assigned Panel exist.
						</div>
					</div>
				</cfif>
				
				<h6 class="mt-5">Unassigned Panels</h6>
				<cfif local.qryGetQuestionsWithoutPanel.recordCount>
					<cfloop query="local.qryGetQuestionsWithoutPanel">
						<div class="row questionPanel pt-3 pb-3 m-1 border-bottom border-secondary">
							<div class="col-10 p-0">
								#local.qryGetQuestionsWithoutPanel.name# (#local.qryGetQuestionsWithoutPanel.panelCount#)
							</div>
							<div class="col-2">
								<cfif local.qryGetQuestionsWithoutPanel.statusName EQ "Active"><i class="fa-solid fa-circle-check text-info" title="#local.qryGetQuestionsWithoutPanel.statusname#"></i><cfelse><i class="fa-solid fa-circle-check text-grey" title="#local.qryGetQuestionsWithoutPanel.statusname#"></i></cfif>
								&nbsp;&nbsp;
								<cfif val(local.qryGetQuestionsWithoutPanel.feDspClientReferral)><i class="fa-regular fa-eye" title="Displays in Front-end"></i><cfelse><i class="fa-regular fa-eye-slash" title="Doesn't display in Front-end"></i></cfif>
							</div>
						</div>
					</cfloop>
				<cfelse>
					<div class="row">
						<div class="col-12">
							No Unassigned Panel exist
						</div>
					</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getPanelRelatedQuestionsContent" access="public" output="false" returntype="struct" hint="I return tree structure of panel related questions.">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = StructNew();
			local.objAdminReferrals = this.objAdminReferrals;
			local.panelID = arguments.event.getValue('panelID',0);
			local.qryGetPanelDetails = this.objAdminReferrals.getPanelByID(panelID=local.panelID);
			if(val(local.qryGetPanelDetails.panelParentID)){
				local.qryGetPanelDetails = this.objAdminReferrals.getPanelByID(panelID=local.qryGetPanelDetails.panelParentID);
				local.siteResourceID = local.qryGetPanelDetails.siteResourceID;
			} else {
				local.siteResourceID = this.siteResourceID;
			}
		</cfscript>

		<cfsavecontent variable="local.panelTreeHTML">
			<cfoutput>
				#getParentQuestionAnswers(panelID=local.panelID)#
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.panelTreeHTML,"echo")>
	</cffunction>

	<cffunction name="getParentQuestionAnswers" access="public" output="false" returntype="string" hint="I return tree structure of panel related questions.">
		<cfargument name="panelID" type="numeric" required="true" default="0" >
		<cfargument name="valueID" type="numeric" required="true" default="0" >
		<cfargument name="fieldID" type="numeric" required="true" default="0" >
		<cfargument name="rowPadding" type="numeric" required="true" default="0" >

		<cfset var local = StructNew()>
		<cfset arguments.rowPadding = arguments.rowPadding + 1>
		<cfsavecontent variable="local.returnHtml">
			<cfoutput>
			<cfif val(arguments.panelID)>
				<cfquery name="local.qryGetAllPanelAnswers" datasource="#application.dsn.membercentral.dsn#">
					select fv.valueID, fv.valueString, p.name as panelName
					from cf_fieldValues fv 
					inner join cf_fieldValueSiteResources  fvs on fvs.valueID = fv.valueID
					inner join ref_panels p on p.siteResourceID = fvs.siteResourceID
					where p.panelid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.panelID#">
					order by fv.valueOrder
				</cfquery>
				<cfif local.qryGetAllPanelAnswers.recordCount>
					<cfloop query="local.qryGetAllPanelAnswers">
						<div class="panelQuestionTree">
							#getParentQuestionAnswers(valueID=local.qryGetAllPanelAnswers.valueID,rowPadding=arguments.rowPadding)#
							<div class="panelQuestionHolder"><div><h6>#local.qryGetAllPanelAnswers.valueString# <cfif LEN(local.qryGetAllPanelAnswers.panelName)><div style="float:right"><i><small>Panel Assigned</small></i></div></cfif></h6></div></div>
						</div>
					</cfloop>
				<cfelse>
					No Questions added
				</cfif>
			</cfif>
			<cfif val(arguments.valueID)>
				<cfquery name="local.qryGetAllPanelAnswers" datasource="#application.dsn.membercentral.dsn#">
					select f.fieldID, f.fieldText
					from cf_fields f 
					inner join cf_fieldValues  fv on fv.fieldID = f.fieldID
					where fv.valueID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.valueID#">
					order by f.fieldOrder
				</cfquery>
				<cfif local.qryGetAllPanelAnswers.recordCount>
					<cfloop query="local.qryGetAllPanelAnswers">
						#getParentQuestionAnswers(fieldID=local.qryGetAllPanelAnswers.fieldID,rowPadding=arguments.rowPadding)#
						<div  class="panelQuestionHolder"><div><h6>#local.qryGetAllPanelAnswers.fieldText#</h6></div></div>
					</cfloop>
				</cfif>
			</cfif>

			<cfif val(arguments.fieldID)>
				<cfquery name="local.qryGetAllPanelAnswers" datasource="#application.dsn.membercentral.dsn#">
					select fvc.condValueID, fv.valueString 
					from  dbo.cf_fieldValueConditions fvc
					inner join cf_fieldValues fv on fv.valueID = fvc.condvalueId
					where fvc.fieldID = #arguments.fieldID#
				</cfquery>

				<cfif local.qryGetAllPanelAnswers.recordCount>
					<cfloop query="local.qryGetAllPanelAnswers">
						#getParentQuestionAnswers(valueID=local.qryGetAllPanelAnswers.condValueID,rowPadding=arguments.rowPadding)#
						<div  class="panelQuestionHolder"><div><h6>#local.qryGetAllPanelAnswers.valueString#</h6></div></div>
					</cfloop>
				</cfif>
			</cfif>

		</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHtml>
	</cffunction>

	<cffunction name="previewReferralEmail" access="public" output="false" returntype="struct" hint="preview referral email">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>
	
		<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals")>
		<cfset local.qryReferrals = local.objAdminReferrals.getClientReferralsIds(Event = arguments.event)>
		<cfset local.template = arguments.event.getValue('template','')>
		<cfset local.templateId = arguments.event.getValue('templateId','')>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgID')>
		<cfset local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname')>
		<cfset local.defaultTimeZoneID = arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')>
		<cfset local.sitename = arguments.event.getValue('mc_siteinfo.sitename')>
		<cfset local.siteCode = arguments.event.getValue('mc_siteinfo.siteCode')>
		<cfset local.orgcode = arguments.event.getValue('mc_siteinfo.orgcode')>
		<cfset local.showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType')>
		<cfset local.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>
		
		<cfset local.referralList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=referralsXML&meth=previewEmailReferralsXML&mode=stream">
		
		<cfset local.previewTitle = ''>
		<cfif local.template eq 'clientReferral'>
			<cfset local.previewTitle = 'E-mail Client Referral: Preview Message'>
		<cfelseif local.template eq 'memberReferral'>	
			<cfset local.previewTitle = 'E-mail Member Referral: Preview Message'>
		<cfelseif local.template eq 'clientReceiptReferral'>	
			<cfset local.previewTitle = 'E-mail Client Receipt: Preview Message'>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_emailTemplatePreview.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<!--- export/import --->
	<cffunction name="exportQAStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "QuestionAnswers.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ref_exportQuestionAnswerStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.manageQuestions#&tab=ex" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="prepareQuestionAnswerImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>
		
		<cfsetting requesttimeout="500">
	
		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>
			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/QuestionAnswers.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/QuestionAnswers.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>
		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/QuestionAnswers.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name in ('sync_cf_fields.bcp','sync_cf_fieldValues.bcp','sync_cf_fieldValueConditions.bcp','sync_cf_fieldValueSiteResources.bcp','sync_ref_fieldValue_fields.bcp','sync_cf_supporting.bcp')
				</cfquery>
				<cfif local.qryFiles.recordcount neq 6>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain five.">
				<cfelseif local.qryFilesCheck.theCount neq 6>
					<cfthrow message="Required files in the backup file is missing.">
				</cfif>
				<cfzip file="#local.strImportFile.strFolder.folderPath#/QuestionAnswers.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/QuestionAnswers.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>
  		<!--- prepare import --->
  		<cfif local.rs.success>
			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Question & Answers Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>
			<cfset local.QuestionAnswersImportStruct = application.mcCacheManager.sessionGetValue(keyname='QuestionAnswersImportStruct', defaultValue={})>
			<cfset local.QuestionAnswersImportStruct[local.threadID] = local.strImportFile.strFolder>
			<cfset application.mcCacheManager.sessionSetValue(keyname='QuestionAnswersImportStruct', value=local.QuestionAnswersImportStruct)>
			
			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareQuestionAnswerImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>
			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div id="loadingGif" class="row">
					<div class="pl-3">
						<i class="fa-light fa-circle-notch fa-spin fa-4x fa-pull-left"></i> 
					</div>
					<div class="col-md-10">
						<div class="display4 pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isQAImportCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<br/><button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.manageQuestions#&tab=ex';">Try Again</button> 
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareQuestionAnswerImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.ref_prepareQuestionAnswerImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showQuestionAnswerImportCompareResults(siteID=arguments.paramStruct.siteID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.manageQuestions#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/QuestionAnswerImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showQuestionAnswerImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>

		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewFields = XMLSearch(arguments.strResult.importResultXML,"/import/newfields/field")>
			<cfset local.strImportResult.arrUpdateFields = XMLSearch(arguments.strResult.importResultXML,"/import/updatefields/field")>
			<cfset local.strImportResult.arrRemoveFields = XMLSearch(arguments.strResult.importResultXML,"/import/removefields/field")>
			<cfset local.strImportResult.arrRemoveValues = XMLSearch(arguments.strResult.importResultXML,"/import/removevalues/value")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif local.hasChanges>
				<cfset local.importCompareReport = generateQuestionAnswerImportCompareReport(siteID=arguments.siteID, threadID=arguments.threadID, strImportResult=local.strImportResult)>
			</cfif>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
			<cfset local.errorReport = generateQuestionAnswerImportErrorReport(siteID=arguments.siteID, arrErrors=local.arrErrors)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row mb-3">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Question & Answers Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div><b>An undetermined error occurred during the import.</b></div>
										</cfif>
									<cfelse>
										<div><b>The import was stopped and requires your attention.</b></div>
										<br/>
										<div>#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<br/>
									<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelQuestionAnswerImport(siteID=arguments.siteID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Question & Answers Import No Action Needed</h4>
				<div>There were no changes to process.</div>
				<br/>

				<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importCompareReport)>
					<div>#local.importCompareReport#</div>
					<br/>
				</cfif>
				<br/>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Question & Answers Import Issue Report</h4>
				<div class="alert alert-danger font-weight-bold">
					An undetermined error occurred during the import.
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateQuestionAnswerImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="arrErrors" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importQuestionAnswerErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateQuestionAnswerImportCompareReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateFields)>
			<cfquery name="local.qryImportFileUpdateFields" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @usageID int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SELECT @usageID = memberCentral.dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

				SELECT DISTINCT [uid], fieldID, fieldText, fieldOrder
				FROM dbo.sync_cf_fields
				WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				AND usageID = @usageID
				AND finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.ReferralsSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals',siteID=arguments.siteID)>

			<cfquery name="local.qryImportFileUpdateFieldValues" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @controllingSiteResourceID int, @usageID int, @groupUID uniqueidentifier;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ReferralsSRID#">;
				SELECT @usageID = memberCentral.dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

				SELECT TOP 1 @groupUID = groupUID
				FROM dbo.sync_cf_fields
				WHERE siteID = @siteID
				AND useCSRID = @controllingSiteResourceID
				AND usageID = @usageID;

				SELECT DISTINCT sf.[uid], sfv.valueString, sfv.valueOrder, sf.useValue, p.name as panelName,
					sfs_field.fieldText as subPanelQuestionTree, sfs_field.[uid] as subPanelQuestionTreeUID
				FROM dbo.sync_cf_fields as sf
				INNER JOIN dbo.sync_cf_fieldValues as sfv on sfv.siteID = @siteID 
					and sfv.groupUID = @groupUID
					and sfv.valueID = sf.valueID
				LEFT OUTER JOIN dbo.sync_cf_fieldValueSiteResources as sfvs
						INNER JOIN dataTransfer.dbo.sync_cf_supporting as sfs on sfs.siteID = @siteID 
							and sfs.groupUID = @groupUID 
							and sfs.cat = 'panel'
							and sfs.itemID = sfvs.siteResourceID
						INNER JOIN memberCentral.dbo.ref_panels as p on p.siteResourceID = sfs.useID
					ON sfvs.valueID = sfv.valueID
						AND sfvs.siteID = @siteID 
						AND sfvs.groupUID = @groupUID 
				LEFT OUTER JOIN dbo.sync_ref_fieldValue_fields as sfvf
						INNER JOIN dataTransfer.dbo.sync_cf_fields as sfs_field on sfs_field.siteID = @siteID 
							and sfs_field.groupUID = @groupUID 
							and sfs_field.fieldID = sfvf.fieldID
					ON sfvf.valueID = sfv.valueID
						AND sfvf.siteID = @siteID 
						AND sfvf.groupUID = @groupUID 
				WHERE sf.siteID = @siteID
				AND sf.groupUID = @groupUID
				AND sf.finalAction = 'C'
				AND sfv.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qrySiteUpdateFields" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @usageID int;
				SELECT @usageID = dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

				select [uid], fieldID, fieldText, fieldOrder
				from dbo.cf_fields
				where usageID = @usageID
				and uid in (#listQualify(valueList(local.qryImportFileUpdateFields.uid), "'")#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qrySiteUpdateFieldValues" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @usageID int;
				SELECT @usageID = dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

				select f.[uid], fv.valueID, fv.fieldID, fv.valueString, fv.valueOrder, p.name as panelName,
					fvf_f.fieldText as subPanelQuestionTree, fvf_f.[uid] as subPanelQuestionTreeUID
				from dbo.cf_fields as f
				inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
				left outer join dbo.cf_fieldValueSiteResources as fvs 
						inner join dbo.ref_panels as p on p.siteResourceID = fvs.siteResourceID
					on fvs.valueID = fv.valueID
				left outer join dbo.ref_fieldValue_fields as fvf 
						inner join dbo.cf_fields as fvf_f on fvf_f.fieldID = fvf.fieldID
							and fvf_f.usageID = @usageID
							and fvf_f.isActive = 1
					on fvf.valueID = fv.valueID
				where f.usageID = @usageID
				and f.fieldID in (0#valueList(local.qrySiteUpdateFields.fieldID)#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryAddNewFieldValuesForUpdateField" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @usageID int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SELECT @usageID = memberCentral.dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

				SELECT DISTINCT sf.[uid], sf.fieldText, sf.fieldValue
				FROM dbo.sync_cf_fields as sf
				INNER JOIN dbo.sync_cf_fieldValues as sfv on sfv.siteID = @siteID and sfv.valueID = sf.valueID
				WHERE sf.siteID = @siteID
				AND sf.usageID = @usageID
				AND sf.finalAction = 'C'
				AND sfv.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateFieldValueConditions" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @controllingSiteResourceID int, @usageID int, @groupUID uniqueidentifier;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ReferralsSRID#">;
				SELECT @usageID = memberCentral.dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

				SELECT TOP 1 @groupUID = groupUID
				FROM dbo.sync_cf_fields
				WHERE siteID = @siteID
				AND useCSRID = @controllingSiteResourceID
				AND usageID = @usageID;

				SELECT DISTINCT sfc.fieldID, sfc.condFieldID, sf.[uid] as fieldUID, sf.fieldText, scf.[uid] as condFieldUID, 
					scf.fieldText as condFieldText, sfc.condExp, sfv.valueString
				FROM dbo.sync_cf_fieldValueConditions as sfc
				INNER JOIN dbo.sync_cf_fields as sf on sf.siteID = @siteID and sf.groupUID = @groupUID
					and sf.fieldID = sfc.fieldID
				INNER JOIN dbo.sync_cf_fields as scf on scf.siteID = @siteID and scf.groupUID = @groupUID
					and scf.fieldID = sfc.condFieldID
				INNER JOIN dbo.sync_cf_fieldValues as sfv on sfv.siteID = @siteID and sfv.groupUID = @groupUID
					and sfv.valueID = sfc.condValueID
				WHERE sfc.siteID = @siteID
				AND sfc.groupUID = @groupUID
				AND sfc.finalAction = 'C'
				AND sf.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompareQuestionAnswerImport.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="cancelQuestionAnswerImport" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">
		<cfset var ReferralsSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals',siteID=arguments.siteID)>

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">, 
					@controllingSiteResourceID int, @usageID int, @groupUID uniqueidentifier;

				SET @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#ReferralsSRID#">;
				SELECT @usageID = memberCentral.dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

				SELECT TOP 1 @groupUID = groupUID
				FROM dbo.sync_cf_fields
				WHERE siteID = @siteID
				AND controllingSiteResourceID = @controllingSiteResourceID
				AND usageID = @usageID;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_cf_fieldValueSiteResources WHERE siteID = @siteID AND groupUID = @groupUID;
					DELETE FROM dbo.sync_ref_fieldValue_fields WHERE siteID = @siteID AND groupUID = @groupUID;
					DELETE FROM dbo.sync_cf_fieldValueConditions WHERE siteID = @siteID AND groupUID = @groupUID;
					DELETE FROM dbo.sync_cf_fieldValues WHERE siteID = @siteID AND groupUID = @groupUID;
					DELETE FROM dbo.sync_cf_fields WHERE siteID = @siteID AND groupUID = @groupUID;
					DELETE FROM dbo.sync_cf_supporting WHERE siteID = @siteID AND groupUID = @groupUID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="doImportQuestionAnswers" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">

		<cfsetting requesttimeout="500">

		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>
		<cfset local.QuestionAnswersImportStruct = application.mcCacheManager.sessionGetValue(keyname='QuestionAnswersImportStruct', defaultValue={})>
		<cfif NOT structKeyExists(local.QuestionAnswersImportStruct,local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Referral Question & Answers. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfstoredproc procedure="ref_importQuestionAnswers" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Question & Answers file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- when done, remove from cache --->
			<cfif structCount(local.QuestionAnswersImportStruct)>
				<cfset StructDelete(local.QuestionAnswersImportStruct, local.threadID)>				
				<cfset application.mcCacheManager.sessionSetValue(keyname='QuestionAnswersImportStruct', value=local.QuestionAnswersImportStruct)>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_questionAnswersImportReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageScheduledJobs" access="public" output="false" returntype="struct" hint="Landing page schedule jobs...">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			local.urlString 	= '';
			local.qryGetAllScheduledReportTypes = this.objAdminReferrals.getScheduledReportTypes();

			local.qryGetCaseStatuses = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID).qryStatus;

			local.qryReferralreminderScheduledReportType = local.qryGetAllScheduledReportTypes.filter(function(row){
				return (arguments.row.scheduledReportTypeName contains 'Referral Reminders');
			});
			
			local.referralReminderScheduleJobList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getScheduleJobList&mode=stream&referralId=#this.referralID#&reportTypeID=#local.qryReferralreminderScheduledReportType.scheduledReportTypeID#";

			if(this.allowFeeDiscrepancy) {
				local.qryGetFeeDiscrepancyStatuses = this.objAdminReferrals.getFeeDiscrepancyStatuses();
				local.qryReferralDiscrepancyScheduledReportType = local.qryGetAllScheduledReportTypes.filter(function(row){
					return (arguments.row.scheduledReportTypeName contains 'Fee Discrepancies');
				});
				local.feeDiscrepancyScheduleJobList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getScheduleJobList&mode=stream&referralId=#this.referralID#&reportTypeID=#local.qryReferralDiscrepancyScheduledReportType.scheduledReportTypeID#";
			}

			local.qryInternalCaseTrackingScheduledReportType = local.qryGetAllScheduledReportTypes.filter(function(row){
				return (arguments.row.scheduledReportTypeName contains 'Internal Case Tracking');
			});

			local.internalCaseTrackingScheduleJobList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getScheduleJobList&mode=stream&referralId=#this.referralID#&reportTypeID=#local.qryInternalCaseTrackingScheduledReportType.scheduledReportTypeID#";

		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_scheduledJobs.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="editScheduleJob" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" />
		<cfscript>
			var local = structNew();
			local.objEmailTemplate = createObject("component","model.admin.emailTemplates.emailTemplates");
			local.scheduleJobId = arguments.event.getValue('scheduleID',0);
			local.scheduledReportTypeID = arguments.event.getValue('scheduledReportTypeID',0);
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.qryGetAllScheduledReportTypes = this.objAdminReferrals.getScheduledReportTypes();
			local.qryAllAFs = this.objAdminReferrals.getAdvanceFormula(local.siteID);
			var scheduledReportTypeID = local.scheduledReportTypeID;
			local.qryGetScheduledReportType = local.qryGetAllScheduledReportTypes.filter(function(row){
				return (arguments.row.scheduledReportTypeID eq scheduledReportTypeID);
			});
			local.scheduledReportTypeName = local.qryGetScheduledReportType.scheduledReportTypeName;
			local.qryGetFeeDiscrepancyStatuses = QueryNew('');
			local.referralStatusResponseStruct = this.objAdminReferrals.getClientReferralStatus(referralID=this.referralID, isClosedNisOpen=1);
			if(local.referralStatusResponseStruct.success){
				local.qryGetCaseStatuses = local.referralStatusResponseStruct.qryStatus;
			} else {
				return showRefError('Open and Closed Referral Statuses are not defined. Please contact administrator.');
			}
			local.clientReferralStatusIDs = '';

			if(local.scheduledReportTypeName EQ 'Fee Discrepancies') {
				local.qryGetClientEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFFEEDSCRPNCY');
				local.qryGetFeeDiscrepancyStatuses = this.objAdminReferrals.getFeeDiscrepancyStatuses(isScheduleJob=1);
				local.feeDiscrepancyStatusID = local.qryGetFeeDiscrepancyStatuses.feeDiscrepancyStatusID;
			} else if(local.scheduledReportTypeName EQ 'Internal Case Tracking') {
				local.qryGetClientEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFCSTRACKREPORT');
			} else
				local.qryGetClientEmailTemplate = local.objEmailTemplate.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode='ETREFREPORT');

			local.formAction = this.link.saveScheduleJob ;
			local.scheduleJobName = '' ;
			local.fromEmail	= '' ;
			local.fromRefDate = '' ;
			local.toRefDate	= '' ;
			local.startDate	= '' ;
			local.templateId = 0 ;
			local.clientTemplateId = 0 ;
			local.cutoffDate = '' ;
			local.lastUpdatedDate = '';
			local.fromFollowUpDate = '';
			local.toFollowUpDate = '';
			local.status = 1 ;
			local.startDateAFID = 0;
			local.fromRefDateAFID = 0;
			local.toRefDateAFID = 0;
			local.cutoffDateAFID = 0;
			local.lastUpdatedDateAFID = 0;
			local.toFollowUpDateAFID = 0;
			local.fromFollowUpDateAFID = 0;
			
			if(val(local.scheduleJobId)){
				local.qryScheduleJobs = getScheduleJobsById(scheduleJobId = local.scheduleJobId);
				local.scheduleJobName	= local.qryScheduleJobs.name ;
				local.scheduledReportTypeID = local.qryScheduleJobs.scheduledReportTypeID ;
				local.scheduledReportTypeName = local.qryScheduleJobs.scheduledReportTypeName ;
				local.fromEmail	= local.qryScheduleJobs.fromEmail ;
				local.fromRefDate	= dateFormat(local.qryScheduleJobs.fromRefDate,"mm/dd/yyyy") ;
				local.toRefDate	= dateFormat(local.qryScheduleJobs.toRefDate,"mm/dd/yyyy");
				local.startDate	= dateTimeFormat(local.qryScheduleJobs.nextRunDate,"mm/dd/yyyy HH:nn");
				local.templateId	= local.qryScheduleJobs.templateID ;
				local.clientTemplateId	= local.qryScheduleJobs.clientTemplateId ;
				local.cutoffDate	= dateFormat(local.qryScheduleJobs.cutoffDate,"mm/dd/yyyy") ;
				local.lastUpdatedDate = dateFormat(local.qryScheduleJobs.lastUpdatedDate,"mm/dd/yyyy") ;
				local.fromFollowUpDate = dateFormat(local.qryScheduleJobs.fromFollowUpDate,"mm/dd/yyyy") ;
				local.toFollowUpDate = dateFormat(local.qryScheduleJobs.toFollowUpDate,"mm/dd/yyyy") ;
				local.status	= local.qryScheduleJobs.isActive ;
				local.startDateAFID = local.qryScheduleJobs.startDateAFID;
				local.fromRefDateAFID = local.qryScheduleJobs.fromRefDateAFID;
				local.toRefDateAFID = local.qryScheduleJobs.toRefDateAFID;
				local.cutoffDateAFID = local.qryScheduleJobs.cutoffDateAFID;
				local.lastUpdatedDateAFID = local.qryScheduleJobs.lastUpdatedDateAFID;
				local.toFollowUpDateAFID = local.qryScheduleJobs.toFollowUpDateAFID;
				local.fromFollowUpDateAFID = local.qryScheduleJobs.fromFollowUpDateAFID;
				if(len(local.qryScheduleJobs.feeDiscrepancyStatusID))
					local.feeDiscrepancyStatusID = local.qryScheduleJobs.feeDiscrepancyStatusID;
				if(len(local.qryScheduleJobs.clientReferralStatusIDs))
					local.clientReferralStatusIDs = local.qryScheduleJobs.clientReferralStatusIDs;
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_scheduleJob.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveScheduleJob" access="public" output="false" returntype="struct" hint="saves schedule job">
		<cfargument name="event" type="any" />

		<cfset var local = structNew() />
		<cfset local.data = "" />

		<cfset local.scheduledReportTypeID = arguments.event.getValue("scheduledReportTypeID",1)>
		<cfset local.scheduledReportTypeName = arguments.event.getValue("scheduledReportTypeName",'Referral Reminders')>
		<cfset local.scheduleJobName = arguments.event.getValue("scheduleJobName")>
		<cfset local.fromEmail = arguments.event.getValue("fromEmail")>
		<cfset local.templateId = arguments.event.getValue("templateId")>
		<cfset local.startDate = parseDateTime("#replace(arguments.event.getValue('startDate'),' - ',' ')#")>
		<cfset local.fromRefDate = arguments.event.getValue("fromRefDate")>
		<cfset local.toRefDate = arguments.event.getValue("toRefDate")>
		<cfset local.cutoffDate = arguments.event.getValue("cutoffDate",'')>
		<cfset local.lastUpdatedDate = arguments.event.getValue("lastUpdatedDate",'')>
		<cfset local.fromFollowUpDate = arguments.event.getValue("fromFollowUpDate",'')>
		<cfset local.toFollowUpDate = arguments.event.getValue("toFollowUpDate",'')>
		<cfset local.startDateAFID = arguments.event.getValue("startDateAFID",'')>
		<cfset local.fromRefDateAFID = arguments.event.getValue("fromRefDateAFID",'')>
		<cfset local.toRefDateAFID = arguments.event.getValue("toRefDateAFID",'')>
		<cfset local.cutoffDateAFID = arguments.event.getValue("cutoffDateAFID",'')>
		<cfset local.lastUpdatedDateAFID = arguments.event.getValue("lastUpdatedDateAFID",'')>
		<cfset local.toFollowUpDateAFID = arguments.event.getValue("toFollowUpDateAFID",'')>
		<cfset local.fromFollowUpDateAFID = arguments.event.getValue("fromFollowUpDateAFID",'')>

		<cfset local.status = arguments.event.getValue("status",0)>
		<cfset local.scheduleJobId = int(val(arguments.event.getValue("scheduleJobId",0)))>
		<cfif local.scheduledReportTypeName EQ "Fee Discrepancies">
			<cfset local.feeDiscrepancyStatusID = arguments.event.getValue("feeDiscrepancyStatusID",0)>
			<cfset local.clientTemplateId = arguments.event.getValue("clientTemplateId",0)>
		</cfif>
		<cfset local.clientReferralStatusIDs = arguments.event.getValue("clientReferralStatusID",'')>

		<cfset local.arrTypeSpecificDateFields = [
			{ label:"Next Run Date", dbFieldName:"nextRunDate", dbFieldNameAF:"startDateAFID" },
			{ label:"Referral From Date", dbFieldName:"fromRefDate", dbFieldNameAF:"fromRefDateAFID" },
			{ label:"Referral To Date", dbFieldName:"toRefDate", dbFieldNameAF:"toRefDateAFID" }
		]>
		<cfif local.scheduledReportTypeName EQ "Internal Case Tracking">
			<cfset ArrayAppend(local.arrTypeSpecificDateFields, [
					{ label:"Follow Up Date From", dbFieldName:"fromFollowUpDate", dbFieldNameAF:"fromFollowUpDateAFID" },
					{ label:"Follow Up Date To", dbFieldName:"toFollowUpDate", dbFieldNameAF:"toFollowUpDateAFID" },
					{ label:"Last Updated Date", dbFieldName:"lastUpdatedDate", dbFieldNameAF:"lastUpdatedDateAFID" }
				], "true")>
		<cfelseif local.scheduledReportTypeName EQ "Referral Reminders">
			<cfset ArrayAppend(local.arrTypeSpecificDateFields, [
					{ label:"Last Updated Date", dbFieldName:"lastUpdatedDate", dbFieldNameAF:"lastUpdatedDateAFID" },
					{ label:"Cutoff Date", dbFieldName:"cutoffDate", dbFieldNameAF:"cutoffDateAFID" }
				], "true")>
		<cfelse>
			<cfset ArrayAppend(local.arrTypeSpecificDateFields, { label:"Cutoff Date", dbFieldName:"cutoffDate", dbFieldNameAF:"cutoffDateAFID" })>
		</cfif>

		<cfquery name="local.qrySaveScheduledJob" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

			DECLARE @fromRefDate DATETIME, @toRefDate DATETIME, @lastUpdatedDate DATETIME,
				@toFollowUpDate DATETIME, @fromFollowUpDate DATETIME, @cutoffDate DATETIME, @startDateAFID INT,
				@fromRefDateAFID INT, @toRefDateAFID INT, @cutoffDateAFID INT, @lastUpdatedDateAFID INT,
				@toFollowUpDateAFID INT, @fromFollowUpDateAFID INT, @name VARCHAR(255), @fromEmail VARCHAR(255),
				@templateID INT, @referralID INT, @nextRunDate DATETIME, @feeDiscrepancyStatusID INT, @clientTemplateId INT,
				@status INT, @clientReferralStatusIDs VARCHAR(200), @templateName VARCHAR(300), @clientTemplateName VARCHAR(300),
				@caseStatusList VARCHAR(max), @caseStatusListOld VARCHAR(max), @feeStatus VARCHAR(255), @oldName VARCHAR(255),
				@loggedinMemberId INT, @scheduleReportID INT, @scheduledReportTypeID INT, @orgID INT, @siteID INT,
				@crlf VARCHAR(10), @msgjson VARCHAR(max);

			set @startDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.startDateAFID)#">,0);
			set @toRefDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.toRefDateAFID)#">,0);
			set @fromRefDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.fromRefDateAFID)#">,0);
			<cfif local.scheduledReportTypeName EQ "Internal Case Tracking">
				SET @lastUpdatedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.lastUpdatedDate)#">;
				SET @fromFollowUpDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.fromFollowUpDate)#">;
				SET @toFollowUpDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.toFollowUpDate)#">;
				SET @lastUpdatedDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.lastUpdatedDateAFID)#">,0);
				SET @toFollowUpDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.toFollowUpDateAFID)#">,0);
				SET @fromFollowUpDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.fromFollowUpDateAFID)#">,0);
			<cfelseif local.scheduledReportTypeName EQ "Referral Reminders">
				SET @lastUpdatedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.lastUpdatedDate)#">;
				SET @cutoffDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.cutoffDate)#">;
				SET @lastUpdatedDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.lastUpdatedDateAFID)#">,0);
				SET @cutoffDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.cutoffDateAFID)#">,0);
			<cfelse>
				SET @cutoffDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.cutoffDate)#">;
				SET @cutoffDateAFID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.cutoffDateAFID)#">,0);
				SET @feeDiscrepancyStatusID = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.feeDiscrepancyStatusID)#">,0);
				SET @clientTemplateId = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.clientTemplateId)#">,0);
			</cfif>

			SET @name = <cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(local.scheduleJobName)#">;
			SET @fromEmail = <cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(local.fromEmail)#">;
			SET @referralID = <cfqueryparam cfsqltype="cf_sql_integer" value="#trim(this.referralID)#">;
			SET @templateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#trim(local.templateId)#">;
			SET @nextRunDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.startDate)#">;
			SET @fromRefDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.fromRefDate)#">;
			SET @toRefDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#trim(local.toRefDate)#">;		
			SET @status = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.status)#">;
			SET @clientReferralStatusIDs = <cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(local.clientReferralStatusIDs)#">;
			SET @loggedinMemberId = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberData.memberid#">;
			SET @scheduleReportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#trim(local.scheduleJobId)#">;
			SET @scheduledReportTypeID  = <cfqueryparam cfsqltype="cf_sql_integer" value="#trim(local.scheduledReportTypeID)#">;
			SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @crlf = char(13) + char(10);

			IF OBJECT_ID('tempdb..##tmpAF') IS NOT NULL
				DROP TABLE ##tmpAF;
			CREATE TABLE ##tmpAF (AFID INT, afName VARCHAR(255));

			INSERT INTO ##tmpAF (AFID, afName)
			SELECT DISTINCT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = @siteID;

			SELECT @templateName = templateName
			FROM dbo.et_emailTemplates
			WHERE templateID = @templateId;

			SELECT @caseStatusList = STUFF((SELECT ', ' + statusName 
			FROM dbo.ref_clientReferralStatus AS fcrs 
			WHERE CHARINDEX(',' + CAST(fcrs.clientReferralStatusID AS VARCHAR(10)) + ',', ',' + @clientReferralStatusIDs + ',') > 0
			AND fcrs.referralID = @referralID
			FOR XML PATH('')), 1, 2, '');

			<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
				SELECT @clientTemplateName = templateName
				FROM dbo.et_emailTemplates
				WHERE templateID = @clientTemplateId;

				SELECT @feeStatus = statusName
				FROM dbo.ref_feeDiscrepancyStatuses
				WHERE feeDiscrepancyStatusID = @feeDiscrepancyStatusID;
			</cfif>

			<cfif local.scheduleJobId EQ 0>
				BEGIN TRAN;
					INSERT INTO dbo.ref_scheduledReports (	
						name, fromEmail, referralID, TemplateID, nextRunDate,
						fromRefDate, toRefDate, startDateAFID, fromRefDateAFID, toRefDateAFID,
						<cfif local.scheduledReportTypeName EQ "Internal Case Tracking">
							lastUpdatedDate, fromFollowUpDate, toFollowUpDate, lastUpdatedDateAFID, fromFollowUpDateAFID, toFollowUpDateAFID,
						<cfelseif local.scheduledReportTypeName EQ "Referral Reminders">
							lastUpdatedDate, lastUpdatedDateAFID, cutoffDate, cutoffDateAFID,
						<cfelse>
							cutoffDate, cutoffDateAFID,
							<cfif val(local.feeDiscrepancyStatusID)>
								feeDiscrepancyStatusID, clientTemplateId,
							</cfif>
						</cfif>
						isActive, createdDate, createdBy, modifiedDate, modifiedBy, scheduledReportTypeID, clientReferralStatusIDs
					) VALUES (
						@name, @fromEmail, @referralID, @templateId, @nextRunDate, @fromRefDate, @toRefDate, @startDateAFID, @fromRefDateAFID, @toRefDateAFID,
						<cfif local.scheduledReportTypeName EQ "Internal Case Tracking">
							@lastUpdatedDate, @fromFollowUpDate, @toFollowUpDate, @lastUpdatedDateAFID, @fromFollowUpDateAFID, @toFollowUpDateAFID,
						<cfelseif local.scheduledReportTypeName EQ "Referral Reminders">
							@lastUpdatedDate, @lastUpdatedDateAFID, @cutoffDate, @cutoffDateAFID,
						<cfelse>
							@cutoffDate, @cutoffDateAFID,
							<cfif val(local.feeDiscrepancyStatusID)>
								@feeDiscrepancyStatusID, @clientTemplateId,
							</cfif>
						</cfif>
						@status, <cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">,
						@loggedinMemberId, <cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">,
						@loggedinMemberId, @scheduledReportTypeID, @clientReferralStatusIDs
					)

					SELECT @scheduleReportID = SCOPE_IDENTITY();

					SET @msgjson = 'New #local.scheduledReportTypeName# Scheduled Job [' + @name + '] has been created.'
						+ @crlf + 'From Email: ' + @fromEmail
						<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
							+ CASE WHEN len(@feeStatus) > 0 THEN @crlf + 'Fee Discrepancy Status: ' + @feeStatus ELSE '' END
							+ CASE WHEN len(@caseStatusList) > 0 THEN @crlf + 'Case Status: ' + @caseStatusList ELSE '' END
							+ CASE WHEN len(@templateName) > 0 THEN @crlf + 'Template: ' + @templateName ELSE '' END
							+ CASE WHEN len(@clientTemplateName) > 0 THEN @crlf + 'Client Template: ' + @clientTemplateName ELSE '' END
						<cfelse>
							+ CASE WHEN len(@caseStatusList) > 0 THEN @crlf + 'Case Status: ' + @caseStatusList ELSE '' END
							+ CASE WHEN len(@templateName) > 0 THEN @crlf + 'Template: ' + @templateName ELSE '' END
						</cfif>
						<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
							+ CASE WHEN len(@#local.thisField.dbFieldName#) > 0
								THEN @crlf + '#local.thisField.label#: ' + FORMAT(@#local.thisField.dbFieldName#, 'MM/dd/yyyy<cfif local.thisField.label eq "Next Run Date"> - h:mm tt</cfif>')
								ELSE ''
							END
						</cfloop>
						+ CASE WHEN @status = 1 THEN @crlf + 'Status: Active' ELSE @crlf + 'Status: Inactive' END
						<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
							+ CASE WHEN ISNULL(@#local.thisField.dbFieldNameAF#,0) > 0 THEN @crlf + '#local.thisField.label# (Advance): ' + (SELECT afName FROM ##tmpAF WHERE AFID = @#local.thisField.dbFieldNameAF#) ELSE '' END
						</cfloop>;

					SET @msgjson = STRING_ESCAPE(@msgjson,'json');

					EXEC dbo.ref_insertAuditLog	@orgID=@orgID, @siteID=@siteID, @areaCode='SCHEDJOBS', @msgjson=@msgjson, @enteredByMemberID=@loggedinMemberId;
				COMMIT TRAN;
			<cfelse>

				SELECT @caseStatusListOld = STUFF((
					SELECT ', ' + statusName 
					FROM dbo.ref_clientReferralStatus AS fcrs 
					WHERE CHARINDEX(',' + CAST(fcrs.clientReferralStatusID AS VARCHAR(10)) + ',', ',' + sr.clientReferralStatusIDs + ',') > 0
					AND fcrs.referralID = @referralID
					FOR XML PATH('')), 1, 2, '')
				FROM dbo.ref_scheduledReports AS sr
				WHERE sr.scheduleReportID = @scheduleReportID;

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;

				CREATE TABLE ##tmpAuditLogData 	([rowCode] varchar(20) PRIMARY KEY, [Name] varchar(max), [From Email] varchar(max),
					<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
						[Fee Discrepancy Status] varchar(max), [Case Status] varchar(max), [Attorney Template] varchar(max), [Client Template] varchar(max),
					<cfelse>
						[Case Status] varchar(max), [Template] varchar(max),
					</cfif>
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						[#local.thisField.label#] varchar(max),
					</cfloop>
					[Status] VARCHAR(max)
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						, [#local.thisField.label# (Advance)] varchar(max)
					</cfloop>
				);

				INSERT INTO ##tmpAuditLogData ([rowCode], [Name], [From Email], 
					<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
						[Fee Discrepancy Status], [Case Status], [Attorney Template], [Client Template],
					<cfelse>
						[Case Status], [Template],
					</cfif>
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						[#local.thisField.label#],
					</cfloop>
					[Status]
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						, [#local.thisField.label# (Advance)]
					</cfloop>
				)
				SELECT 'OLDVAL', sr.name, sr.fromEmail,
					<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
						(SELECT ffds.statusName FROM dbo.ref_feeDiscrepancyStatuses AS ffds WHERE ffds.feeDiscrepancyStatusID = sr.feeDiscrepancyStatusID),
						@caseStatusListOld,
						(SELECT et.templateName FROM dbo.et_emailTemplates AS et WHERE et.templateID = sr.templateID),
						(SELECT etc.templateName FROM dbo.et_emailTemplates AS etc WHERE etc.templateID = sr.clientTemplateId),
					<cfelse>
						@caseStatusListOld,
						(SELECT et.templateName FROM dbo.et_emailTemplates AS et WHERE et.templateID = sr.templateID),
					</cfif>
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						FORMAT(sr.#local.thisField.dbFieldName#, 'MM/dd/yyyy<cfif local.thisField.label eq "Next Run Date"> - h:mm tt</cfif>'),
					</cfloop>
					CASE WHEN sr.isActive = '1' THEN 'Active' ELSE 'Inactive' END
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						, (SELECT afName FROM ##tmpAF WHERE AFID = sr.#local.thisField.dbFieldNameAF#)
					</cfloop>
				FROM dbo.ref_scheduledReports AS sr
				WHERE sr.scheduleReportID = @scheduleReportID;

				INSERT INTO ##tmpAuditLogData ([rowCode], [Name], [From Email],
					<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
						[Fee Discrepancy Status], [Case Status], [Attorney Template], [Client Template],
					<cfelse>
						[Case Status], [Template],
					</cfif>
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						[#local.thisField.label#],
					</cfloop>
					[Status]
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						, [#local.thisField.label# (Advance)]
					</cfloop>
				)
				VALUES ('NEWVAL', @name, @fromEmail, 
					<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
						@feeStatus, @caseStatusList, @templateName, @clientTemplateName,
					<cfelse>
						@caseStatusList, @templateName,
					</cfif>
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						FORMAT(@#local.thisField.dbFieldName#, 'MM/dd/yyyy<cfif local.thisField.label eq "Next Run Date"> - h:mm tt</cfif>'),
					</cfloop>
					(SELECT CASE WHEN @status = 1 THEN 'Active' ELSE 'Inactive' END)
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						, (SELECT afName FROM ##tmpAF WHERE AFID = @#local.thisField.dbFieldNameAF#)
					</cfloop>
				),
				(
					'DATATYPECODE', 'STRING', 'STRING',
					<cfif local.scheduledReportTypeName EQ "Fee Discrepancies" AND val(local.feeDiscrepancyStatusID)>
						'STRING', 'STRING', 'STRING', 'STRING',
					<cfelse>
						'STRING', 'STRING',
					</cfif>
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						'STRING',
					</cfloop>
					'STRING'
					<cfloop array="#local.arrTypeSpecificDateFields#" item="local.thisField">
						, 'STRING'
					</cfloop>
				);

				SELECT @oldName = [Name] FROM ##tmpAuditLogData WHERE rowCode = 'OLDVAL';

				EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msgjson OUTPUT;

				BEGIN TRAN;

					UPDATE dbo.ref_scheduledReports
					SET name = @name, fromEmail = @fromEmail, templateID = @templateID, nextRunDate = @nextRunDate, fromRefDate = @fromRefDate,
						toRefDate = @toRefDate, startDateAFID=@startDateAFID, fromRefDateAFID=@fromRefDateAFID, toRefDateAFID=@toRefDateAFID,
						<cfif local.scheduledReportTypeName EQ "Internal Case Tracking">
							lastUpdatedDate = @lastUpdatedDate, fromFollowUpDate = @fromFollowUpDate, toFollowUpDate = @toFollowUpDate,
							lastUpdatedDateAFID=@lastUpdatedDateAFID, fromFollowUpDateAFID=@fromFollowUpDateAFID, toFollowUpDateAFID=@toFollowUpDateAFID,
						<cfelseif local.scheduledReportTypeName EQ "Referral Reminders">
							lastUpdatedDate = @lastUpdatedDate, lastUpdatedDateAFID=@lastUpdatedDateAFID, cutoffDate = @cutoffDate, cutoffDateAFID=@cutoffDateAFID,
						<cfelse>
							cutoffDate = @cutoffDate, cutoffDateAFID=@cutoffDateAFID,
							<cfif val(local.feeDiscrepancyStatusID)>
								feeDiscrepancyStatusID = @feeDiscrepancyStatusID, clientTemplateId = @clientTemplateId,
							</cfif>
						</cfif>
						isActive = @status, clientReferralStatusIDs = @clientReferralStatusIDs,
						modifiedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">,
						modifiedBy = @loggedinMemberId
					WHERE scheduleReportID = @scheduleReportID;
					
					-- audit log
					IF ISNULL(@msgjson,'') <> '' BEGIN
						SET @msgjson = STRING_ESCAPE('#local.scheduledReportTypeName# Scheduled Job [' + @oldName + '] has been updated.','json')
							+ @crlf + 'The following changes have been made:' + @crlf + @msgjson;

						EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SCHEDJOBS', @msgjson=@msgjson, @enteredByMemberID=@loggedinMemberId;
					END
				COMMIT TRAN;

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpAF') IS NOT NULL
				DROP TABLE ##tmpAF;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.refreshScheduledJobGrids();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="getScheduleJobsById" access="public" returntype="query">
		<cfargument name="scheduleJobId" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfset local.scheduleJobId = arguments.scheduleJobId>
		
		<cfquery name="local.qryGetScheduleJobs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
			SELECT sr.scheduleReportID,sr.name,sr.fromEmail,
				sr.templateID, sr.ClientTemplateID, sr.nextRunDate,
				sr.cutoffDate, sr.fromRefDate, sr.toRefDate,
				sr.isActive,sr.createdDate,sr.createdBy,sr.modifiedDate,sr.modifiedBy,
				et.templateName,	
				et2.templateName as clientTemplateName,
				af.afName as frequencyName,
				CASE
					WHEN sr.isActive = '1' THEN 'Yes '
					ELSE 'No'
				END as statusText,
				sr.scheduledReportTypeID,
				srt.scheduledReportTypeName,
				ISNULL(sr.feeDiscrepancyStatusID,0) as feeDiscrepancyStatusID,
				fds.statusName as feeDiscrepancyStatusName,
				sr.clientReferralStatusIDs,
				fromFollowUpDate,
				toFollowUpDate,
				lastUpdatedDate,
				startDateAFID,
				fromRefDateAFID,
				toRefDateAFID,
				cutoffDateAFID,
				lastUpdatedDateAFID,
				toFollowUpDateAFID,
				fromFollowUpDateAFID
			FROM dbo.ref_scheduledReports as sr
			INNER JOIN dbo.ref_scheduledReportTypes as srt on srt.scheduledReportTypeID = sr.scheduledReportTypeID
			INNER JOIN dbo.et_emailTemplates as et ON et.templateID = sr.templateID
			LEFT JOIN dbo.af_advanceFormulas as af on af.AFID = sr.startDateAFID
			LEFT JOIN dbo.et_emailTemplates as et2 ON et2.templateID = sr.clientTemplateID
			LEFT JOIN dbo.ref_feeDiscrepancyStatuses as fds on fds.feeDiscrepancyStatusID = sr.feeDiscrepancyStatusID
			WHERE sr.scheduleReportID = <cfqueryparam value="#local.scheduleJobId#" cfsqltype="cf_sql_integer"  />;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfreturn local.qryGetScheduleJobs />
	</cffunction>

	<cffunction name="testScheduleJobMail" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" />
		
		<cfset var local = structNew() />
		<cfset local.scheduleJobId = arguments.event.getValue('scheduleJobId',0)>
		<cfset local.qryScheduleJobData = getScheduleJobsById(scheduleJobId=local.scheduleJobId) />
		<cfset local.referralList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=referralsXML&meth=membersWithReferralsXML&sjID=#local.scheduleJobId#&mode=stream">
		<cfif local.qryScheduleJobData.scheduledReportTypeName EQ 'Internal Case Tracking'>
			<cfset local.referralList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=referralsXML&meth=counselorsWithReferralsXML&sjID=#local.scheduleJobId#&mode=stream">
		</cfif>

		<cfset local.showReceiverOption = false>
		<cfset local.clientList = ''>
		<cfset local.clientTemplateId	= 0>
		<cfset local.templateId = local.qryScheduleJobData.templateID>
		<cfset local.template = 'reportMemberReferral'>
		<cfif local.qryScheduleJobData.scheduledReportTypeName EQ 'Fee Discrepancies'>
			<cfset local.template = 'feeDiscrepancy'>
			<cfif local.qryScheduleJobData.feeDiscrepancyStatusName EQ 'No Fees Reported'>
				<cfset local.showReceiverOption = true>
				<cfset local.clientTemplateId	= local.qryScheduleJobData.clientTemplateId>
				<cfset local.clientList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=referralsXML&meth=clientsXML&sjID=#local.scheduleJobId#&mode=stream">
			</cfif>
		<cfelseif local.qryScheduleJobData.scheduledReportTypeName EQ 'Internal Case Tracking'>
			<cfset local.template = 'internalCaseTracking'>
		</cfif>
		
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgID')>
		<cfset local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname')>
		<cfset local.defaultTimeZoneID = arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')>
		<cfset local.sitename = arguments.event.getValue('mc_siteinfo.sitename')>
		<cfset local.siteCode = arguments.event.getValue('mc_siteinfo.siteCode')>
		<cfset local.orgcode = arguments.event.getValue('mc_siteinfo.orgcode')>
		<cfset local.showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType')>
		<cfset local.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>
		<cfif not local.qryScheduleJobData.recordcount>
			<cfreturn returnAppStruct("That Schedule Job was not found.","echo") />
		</cfif>
		<cfset local.recipientEmail = "">
		<cfif Len(session.cfcuser.memberdata.email) gt 0>
			<cfset local.recipientEmail =  session.cfcuser.memberdata.email>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_scheduleJobSendEmail.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="testSurveyMail" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" />
		
		<cfset var local = structNew() />
		<cfset local.surveyID = arguments.event.getValue('surveyID',0)>
		<cfset local.qrySurveyData = this.objAdminReferrals.getSurveyContent(surveyID=local.surveyID) />
		<cfset local.qryReferrals = this.objAdminReferrals.getClientReferralsIds(Event = arguments.event)>
		<cfset local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname')>
		<cfset local.defaultTimeZoneID = arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')>
		<cfset local.sitename = arguments.event.getValue('mc_siteinfo.sitename')>
		<cfset local.showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType')>
		<cfset local.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>
		<cfset local.referralList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=referralsXML&meth=previewEmailReferralsXML&mode=stream">
		<cfif not local.qrySurveyData.recordcount>
			<cfreturn returnAppStruct("That Survey was not found.","echo") />
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_surveySendEmail.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="manageReferralsLanguage" access="public" output="false" returntype="struct" hint="Landing page...">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.languageListJSONLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralLanguages&mode=stream&referralID=#this.referralID#">

		<cfset appendBreadCrumbs(arguments.event,{ link='', text='Languages' })>
		
		<cfsavecontent variable="local.data">
			<cfoutput>	
				<cfinclude template="dsp_language.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addLanguage" access="public" output="false" returntype="struct" hint="edit language">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />
		<cfset local.languageID = arguments.event.getValue('languageID',0)>
		<cfset local.qrylanguages = getLanguageById(languageID=local.languageID)>
		<cfset local.languageName = local.qrylanguages.languageName>
		<cfset local.languageCode = local.qrylanguages.languageCode>
		<cfset local.isDefault = local.qrylanguages.isDefault>
		<cfset local.isActive = local.qrylanguages.isActive>

		<cfset local.defaultLanguage = ''>
		<cfset local.qryDefaultLanguage = getDefaultLanguage(event = arguments.event)>

		<cfif local.qryDefaultLanguage.recordCount>
			<cfset local.defaultLanguage = local.qryDefaultLanguage.languageName>
		</cfif>

		<cfset local.formAction = this.link.saveLanguage>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_manageLanguage.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveLanguage" access="public" output="false" returntype="struct" hint="save language">
		<cfargument name="event" type="any" />
		
		<cfset var local = structNew() />

		<cfset local.languageID = val(arguments.event.getValue('languageID',0))>
		<cfif local.languageID>
			<cfset this.objAdminReferrals.updateLanguage(arguments.event)>
		<cfelse>
			<cfset this.objAdminReferrals.insertLanguage(arguments.event)>
		</cfif>

		<cfsavecontent variable="local.data">
		<cfoutput>
			<script language="javascript">
				top.reloadLanguageTable();
				top.MCModalUtils.hideModal();
			</script>
		</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="getLanguageByID" access="public" returntype="query">
		<cfargument name="languageID" type="numeric" required="true">
		
		<cfset var qryGetLanguages = "">
		
		<cfquery name="qryGetLanguages" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT languageName, languageCode, isActive, isDefault 
			FROM dbo.ref_languages
			WHERE languageId = <cfqueryparam value="#arguments.languageID#" cfsqltype="cf_sql_integer">;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGetLanguages>
	</cffunction>
	
	<cffunction name="getDefaultLanguage" access="public" returntype="query">
		<cfargument name="event" type="any" />
	
		<cfset var local = structNew() />

		<cfset local.referralID = this.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID>
		
		<cfquery name="local.qryGetDefaultLanguage" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT TOP 1 languageName
			FROM dbo.ref_languages
			WHERE isDefault = 1
			and referralID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.referralID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryGetDefaultLanguage>
	</cffunction>

	<cffunction name="showNoInstanceMessage" access="public" returntype="string">
		<cfset var local = structNew() />

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h3>Referrals Application Instance not found</h3>
				<div class="alert alert-info mt-3">
					<p class="mb-0">Referrals Application Instance must be first created in order to access this module. </p>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.data>	
	</cffunction>

	<cffunction name="addNote" access="public" output="false" returntype="struct" hint="Add Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	

			local.formLink = this.link.saveNote;
			local.urlString = "";
			local.objReferrals	= CreateObject("component","model.referrals.referrals");
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");

			local.clientReferralID = arguments.event.getValue('cID',0);
			local.noteType = arguments.event.getValue('type','C');
			local.noteDateCreated = now();
			local.followUpDate = '';
			local.followUpStatus = ''
			local.referralNote = '';
			local.noteID = 0;
			if(val(arguments.event.getValue('noteID',0))){
				local.noteID = arguments.event.getValue('noteID',0);
				local.qryReferralNote = local.objAdminReferrals.getReferralNotes(referralID=this.referralID,noteID=local.noteID);
				local.referralNote = local.qryReferralNote.referralNote;
				local.noteDateCreated = local.qryReferralNote.createdDate;
				local.noteType = local.qryReferralNote.noteType;
				local.followUpDate = local.qryReferralNote.followUpDate;
				local.followUpStatus = local.qryReferralNote.followUpStatus;
			}
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_note.cfm" />			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="showNotes" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.cID = arguments.event.getValue('cid',0)>
		<cfset local.type = arguments.event.getValue('type',0)>
		<cfset local.referralNoteListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getReferralNotes&cid=#local.cID#&type=#local.type#&referralID=#this.referralID#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showReferralNotes.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="emailFeeSubmissionToClient" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
		
			var local = structNew();
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
			local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname');
			local.defaultTimeZoneID = arguments.event.getValue('mc_siteinfo.defaultTimeZoneID');
			local.sitename = arguments.event.getValue('mc_siteinfo.sitename');
			local.siteCode = arguments.event.getValue('mc_siteinfo.siteCode');
			local.orgcode = arguments.event.getValue('mc_siteinfo.orgcode');
			local.showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType');
			local.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType');
			local.clientID = arguments.event.getValue('clientID',0);
			local.clientReferralID = arguments.event.getValue('clientReferralID',0);

			local.objET = CreateObject("component","model.admin.emailTemplates.emailTemplates");
			local.qryEmailTemplates = local.objET.getCategoriesAndTemplatesForTree(siteID=local.siteID, treeCode="ETREFFEEDSCRPNCY");
			local.qryEmailTemplateCategories = local.objET.getCategoriesForTree(siteID=local.siteID, treeCode="ETREFFEEDSCRPNCY");
			
			local.formlink = buildCurrentLink(arguments.event,"doSendCustomEmailFeeSubmission") & "&mode=stream";
			
			local.qryClient = this.objAdminReferrals.getClient(clientReferralID=local.clientReferralID);
			local.thisClientID = val(local.qryClient.rootClientID);
			if (not local.thisClientID)
				local.thisClientID = val(local.qryClient.clientID);
			
			local.recipientEmail = local.qryClient.email;
			if(not len(local.recipientEmail))
				local.recipientEmail = application.objMember.getMainEmail(memberID=local.qryClient.memberID).email;
			
			local.strEmailData = { resourceTitle='Status Update',resourceTitleDesc='Email this status change update to the Submitter and Submittee', templateEditorLabel='Compose your message. <span class="fr" style="display:block;font-size:1.1em;";<i; class="fa-regular fa-paperclip fa-lg";</i;</span;',
											saveTemplateDesc='<b;Before we e-mail this submission,</b; should we save this message as a template for future use?' };

			local.strFormFields = structNew();
			structInsert(local.strFormFields, 'clientReferralID', local.clientReferralID);
			structInsert(local.strFormFields, 'clientID', local.thisClientID);
				
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_emailFeeSubmission.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doSendCustomEmailFeeSubmission" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.qryGetClient = this.objAdminReferrals.getClient(clientReferralID=arguments.event.getValue('clientReferralID',0))>

		<cfif local.qryGetClient.recordcount is 0>
			<cfset local.retStruct.success = false>
			<cfset local.data = '<h4>No Recipients Found</h4>'>
			<cfset local.data.success = false>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cfset local.memberIDList = valueList(local.qryGetClient.memberID)>
		<cfset local.clientReferralIDList = valueList(local.qryGetClient.clientReferralID)>

		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.event.getTrimValue('templateContent',''), siteid=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.orgName')#">
		<cfset local.emailSubject = arguments.event.getTrimValue('emailSubject','')>
		<cfset local.recipientEmail = arguments.event.getTrimValue('recipientEmail','')>
		<cfset local.emailTemplateCategoryID = arguments.event.getTrimValue('selCategory',0)>
		<cfset local.templateAndSubjectContentToParse = "#local.emailSubject##local.templateContent#">
		<cfset local.emailContentWrapper = application.objEmailWrapper.wrapMessage(emailTitle=local.emailTitle, emailContent=local.templateContent, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<cfif len(local.recipientEmail)>
			<cfloop list="#local.recipientEmail#" index="local.item">
				<cfif not isValid("regex",local.item,application.regEx.email)>
					<cfthrow message="Invalid recipient email address.">
				</cfif>
			</cfloop>
		</cfif>

		<cfif application.MCEnvironment neq "production">
			<cfset local.deliveryReportEmail = "<EMAIL>">
		<cfelseif len(session.cfcuser.memberData.email)>
			<cfset local.deliveryReportEmail = session.cfcuser.memberData.email>
		<cfelse>
			<cfset local.deliveryReportEmail = ''>
		</cfif>

		<!--- if including merge codes that require coldfusion, we need to mark recipient as not ready so we can insert the field below --->
		<cfset local.markRecipientAsReady = 1>
		<cfset local.strRecipientExtMergeTags = application.objMergeCodes.detectExtendedMergeCodes(siteID=arguments.event.getValue('mc_siteinfo.siteid'), rawContent=local.templateAndSubjectContentToParse, extraMergeTagList='feeSubmissionURL')>
		<cfif local.strRecipientExtMergeTags.contentHasMergeCodes>
			<cfset local.markRecipientAsReady = 0>
		</cfif>

		<!--- create message and recipients with merge codes --->
		<cftry>
			<cfif arguments.event.getValue('saveTemplateOption',0) is 1 AND local.emailTemplateCategoryID is 0>
				<cfset local.EmailTemplateAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailTemplateAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
			</cfif>

			<cfquery name="local.qryEmailFeeSubmissionRecipients" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @resourceTypeID int, @parentSiteResourceID int, @emailSubject varchar(200), 
						@templateName varchar(300), @rawContent varchar(max), @contentID int, @siteResourceID int, @toolType varchar(200), 
						@memberID int, @contentVersionID int, @controllingSiteResourceID int, @categoryTreeID int, @categoryTreeCode varchar(20), 
						@categoryTreeName varchar(100), @emailTemplateCategoryID int, @emailTemplateID int, @emailFromName varchar(200), @emailFrom varchar(200), 
						@emailContentWrapper varchar(max), @memberIDList varchar(max),@clientReferralIDList varchar(max),@invoiceIDList varchar(max), @deliveryReportEmail varchar(200),
						@categoryName varchar(200), @sendOnDate datetime, @markRecipientAsReady bit, @recipientEmailList varchar(max);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
					SET @emailSubject = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.emailSubject#">;
					SET @rawContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.templateContent#">;
					SET @emailFromName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('emailFromName','')#">;
					SET @emailFrom = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('emailReplyTo','')#">;
					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @emailContentWrapper = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.emailContentWrapper#">;
					SET @memberIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.memberIDList#">;
					SET @clientReferralIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.clientReferralIDList#">;
					SET @recipientEmailList = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.recipientEmail#">,'');
					SET @toolType = 'ReferralsAdmin';

					<cfif len(local.deliveryReportEmail)>
						SET @deliveryReportEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.deliveryReportEmail#">;
					</cfif>

					<cfif arguments.event.getValue('massEmailScheduling','') eq 'later' and len(arguments.event.getValue('emailDateScheduled',''))>
						SET @sendOnDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('emailDateScheduled'),' - ',' '))#">;
					<cfelse>
						SET @sendOnDate = getDate();
					</cfif>

					SET @markRecipientAsReady = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.markRecipientAsReady#">;
				
					SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
				
					SELECT @parentSiteResourceID = st.siteResourceID 
					FROM dbo.admin_tooltypes tt
					INNER JOIN dbo.admin_siteTools st ON st.toolTypeID = tt.toolTypeID
						AND st.siteID = @siteID
						AND tt.toolType = @toolType
					INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = st.siteResourceID
						AND sr.siteResourceStatusID = 1;

					BEGIN TRAN;
						<!--- email invoice without using template --->
						<cfif arguments.event.getValue('saveTemplateOption',0) is 0>
							EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @parentSiteResourceID=@parentSiteResourceID, 
								@siteResourceStatusID=1, @isHTML=1, @languageID=1, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', 
								@rawContent=@rawContent, @memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_contentLanguages as cl
							inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
								and cv.contentID = @contentID
								and cv.contentLanguageID = cl.contentLanguageID 
								and cv.isActive = 1
							where cl.siteID = @siteID
							and cl.contentID = @contentID
							and cl.languageID = 1;
						
						<!--- create a new email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 1>
							<cfif local.emailTemplateCategoryID is 0>
								set @categoryTreeCode = 'ETREFFEEDSCRPNCY';
								set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EmailTemplateAdminSRID#">;
								set @categoryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('newCategoryName','')#">;

								select @categoryTreeName = categoryTreeName 
								from dbo.cms_categoryTrees 
								where controllingSiteResourceID = @controllingSiteResourceID 
								and categoryTreeCode = @categoryTreeCode;

								select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@controllingSiteResourceID,@categoryTreeName);

								EXEC dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName=@categoryName, @categoryDesc='', @categoryCode='', 
									@parentCategoryID=NULL, @contributorMemberID=@memberID, @categoryID=@emailTemplateCategoryID OUTPUT;
							<cfelse>
								set @emailTemplateCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.emailTemplateCategoryID#">;
							</cfif>

							SET @templateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('templateName','')#">;
							
							EXEC dbo.et_createEmailTemplate @templateTypeCode='ckeditor', @templateName=@templateName, @templateDescription='', 
								@categoryID=@emailTemplateCategoryID, @rawContent=@rawContent, @subjectLine=@emailSubject, 
								@emailFromName=@emailFromName, @emailFrom=@emailFrom, @createdByMemberID=@memberID, 
								@siteID=@siteID, @templateID=@emailTemplateID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.et_emailTemplates as et
							inner join dbo.cms_contentLanguages as cl on cl.siteID = @siteID
								and cl.contentID = et.contentID 
								and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
								and cv.contentID = cl.contentID
								and cv.contentLanguageID = cl.contentLanguageID 
								and cv.isActive = 1
							where et.templateID = @emailTemplateID;

						<!--- update existing email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 2>
							set @emailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fEmailTemplateID',0)#">;

							update dbo.et_emailTemplates
							set subjectLine = @emailSubject,
								emailFromName = @emailFromName,
								emailFrom = @emailFrom
							where templateID = @emailTemplateID;

							select @contentID = contentID 
							from dbo.et_emailTemplates
							where templateID = @emailTemplateID;

							EXEC dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isHTML=1, @contentTitle=@emailSubject, 
								@contentDesc='', @rawcontent=@rawcontent, @memberID=@memberID;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_contentLanguages as cl
							inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
								and cv.contentID = @contentID
								and cv.contentLanguageID = cl.contentLanguageID 
								and cv.isActive = 1
							where cl.siteID = @siteID
							and cl.contentID = @contentID
							and cl.languageID = 1;

						</cfif>
					COMMIT TRAN;

					-- insert and return email recipients
					EXEC dbo.ref_emailFeeSubmission @siteID=@siteID, @memberIDList=@memberIDList, @clientReferralIDList=@clientReferralIDList, @messageToParse=@rawContent, 
						@messageWrapper=@emailContentWrapper, @emailTagTypeID=NULL, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, @emailSubject=@emailSubject, 
						@contentVersionID=@contentVersionID, @recordedByMemberID=@memberID, @deliveryReportEmail=@deliveryReportEmail, @overrideEmailList=@recipientEmailList, 
						@sendOnDate=@sendOnDate, @markRecipientAsReady=@markRecipientAsReady;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("No recipients for message", cfcatch.detail)>
				<cfset local.errorCode = 'noemailrecipient'>
			<cfelse>
				<cfset local.errorCode = ''>
			</cfif>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfreturn returnAppStruct(showMessage(errorCode=local.errorCode),"echo")>
		</cfcatch>
		</cftry>

		<cfif NOT local.markRecipientAsReady>
			<cftry>
				<cfset replaceDetectedExtendedMergeCodes(event=arguments.event, qryGetClient=local.qryGetClient, qryRecipients=local.qryEmailFeeSubmissionRecipients, strRecipientExtMergeTags=local.strRecipientExtMergeTags)>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfreturn returnAppStruct(showMessage(errorCode=''),"echo")>
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					top.closeColorBox();top.reloadPage();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="replaceDetectedExtendedMergeCodes" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="qryGetClient" type="query" required="true">
		<cfargument name="qryRecipients" type="query" required="true">
		<cfargument name="strRecipientExtMergeTags" type="struct" required="true">

		<cfset var local = structNew()>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = arguments.event.getValue('mc_siteinfo.mainHostName')>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfloop query="arguments.qryRecipients">
			<cfset local.strMergeTagArgs = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgcode'),
				recipientID=arguments.qryRecipients.recipientID, messageID=arguments.qryRecipients.messageID, recipientMemberID=arguments.qryRecipients.memberID,
				memberID=arguments.qryRecipients.memberID, membernumber=arguments.qryRecipients.membernumber, hostname=local.thisHostname,
				useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin'), strRecipientExtMergeTags=arguments.strRecipientExtMergeTags }>
			<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>

			<cfset setRecipientAsReady(siteID=arguments.event.getValue('mc_siteinfo.siteid'),messageID=arguments.qryRecipients.messageID,recipientID=arguments.qryRecipients.recipientID)>
		</cfloop>
	</cffunction>

	<cffunction name="setRecipientAsReady" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="qryEmailTemplateData">
			exec dbo.email_setMessageRecipientHistoryStatus
				@siteID= <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,
				@messageID= <cfqueryparam value="#arguments.messageID#" cfsqltype="CF_SQL_INTEGER">,
				@recipientID= <cfqueryparam value="#arguments.recipientID#" cfsqltype="CF_SQL_INTEGER">,
				@statusCode= <cfqueryparam value="Q" cfsqltype="CF_SQL_VARCHAR">,
				@updateDate= <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
		</cfquery>

	</cffunction>

	<cffunction name="showMessage" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.errorCode#">
			<cfcase value="noemailrecipient">
				<cfset local.message = '<h4>No Recipients with Defined Emails</h4><div>No filtered recipients had email addresses defined, so we were not able to send this message.</div>'>
			</cfcase>
			<cfdefaultcase>
				<cfset local.message = "<b>An error occurred. Try again or contact support for assistance.</b>">
			</cfdefaultcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<div class="p-2">#JSStringFormat(local.message)#</div>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showRefError" access="public" output="false" returntype="struct">
		<cfargument name="msg" type="string" required="true">
		<cfscript>
			var local = structNew();
			savecontent variable="local.data" { writeOutput('					
				<div class="alert alert-warning alert-dismissible fade show" role="alert">
					'& arguments.msg &'
				</div>
				<div class="col-12 mt-3 pr-0 text-right">
					<a class="btn btn-sm btn-secondary" href="#this.link.manageClientReferral#" role="button">Go Back to Clients List</a>
				</div>
			'); }	
			return returnAppStruct(local.data,"echo");
		</cfscript>
	</cffunction>
	<cffunction name="previewReferralSMS" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>
		
		<cfset local.objSMSTemplate = createObject("component","model.admin.common.modules.smsTemplate.smsTemplate")>
		<cfset local.template = arguments.event.getValue('template','')>
		<cfset local.templateID = arguments.event.getValue('templateID','')>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>

		<cfset local.strPreview = local.objSMSTemplate.getSMSPreview(Event = arguments.Event)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				#local.strPreview#
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
</cfcomponent>